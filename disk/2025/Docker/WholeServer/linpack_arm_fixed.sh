#!/bin/bash
export LANG=en_US.UTF-8

# ARM平台Linpack测试脚本（修复版本）
echo "[INFO] ARM平台Linpack测试脚本（修复版本）"

# 目录设置
OUTPUT_DIR="/home/<USER>/linpack"
SOFTWARE_DIR="/home/<USER>"
LINPACK_DIR="/home/<USER>/linpack"
LOG_FILE="$OUTPUT_DIR/linpack_run.log"
RESULT_LOG="$OUTPUT_DIR/linpack.log"

mkdir -p "$OUTPUT_DIR" "$SOFTWARE_DIR"

# 检查是否已经编译好HPL
if [ -f "/home/<USER>/hpl_build/hpl-2.3/bin/Linux_ARM/xhpl" ]; then
    echo "[INFO] 发现已编译的HPL，直接使用..." | tee -a "$LOG_FILE"
    
    # 创建linpack目录并复制文件
    mkdir -p "$LINPACK_DIR"
    cp /home/<USER>/hpl_build/hpl-2.3/bin/Linux_ARM/xhpl "$LINPACK_DIR/"
    chmod +x "$LINPACK_DIR/xhpl"
    
    # 设置MPI环境
    export MPI_HOME="/home/<USER>/OpenMPI"
    export PATH="$MPI_HOME/bin:$PATH"
    export LD_LIBRARY_PATH="$MPI_HOME/lib:$LD_LIBRARY_PATH"
    
    # 创建单进程HPL配置文件
    cat > "$LINPACK_DIR/HPL.dat" << 'EOF'
HPLinpack benchmark input file
Innovative Computing Laboratory, University of Tennessee
HPL.out      output file name (if any)
6            device out (6=stdout,7=stderr,file)
1            # of problems sizes (N)
1000         Ns
1            # of NBs
128          NBs
0            PMAP process mapping (0=Row-,1=Column-major)
1            # of process grids (P x Q)
1            Ps
1            Qs
16.0         threshold
1            # of panel fact
2            PFACTs (0=left, 1=Crout, 2=Right)
1            # of recursive stopping criterium
4            NBMINs (>= 1)
1            # of panels in recursion
2            NDIVs
1            # of recursive panel fact.
2            RFACTs (0=left, 1=Crout, 2=Right)
1            # of broadcast
1            BCASTs (0=1rg,1=1rM,2=2rg,3=2rM,4=Lng,5=LnM)
1            # of lookahead depth
1            DEPTHs (>=0)
2            SWAP (0=bin-exch,1=long,2=mix)
64           swapping threshold
0            L1 in (0=transposed,1=no-transposed) form
0            U  in (0=transposed,1=no-transposed) form
1            Equilibration (0=no,1=yes)
8            memory alignment in double (> 0)
EOF
    
    echo "[INFO] 开始HPL测试..." | tee -a "$LOG_FILE"
    cd "$LINPACK_DIR" || exit 1
    
    # 运行HPL测试
    if timeout 600 mpirun --allow-run-as-root -np 1 ./xhpl > "$OUTPUT_DIR/linpack_full.log" 2>&1; then
        echo "[INFO] HPL测试完成" | tee -a "$LOG_FILE"
        
        # 显示测试输出
        echo "[DEBUG] HPL测试输出:" | tee -a "$LOG_FILE"
        cat "$OUTPUT_DIR/linpack_full.log" | tee -a "$LOG_FILE"
        
        # 提取结果
        average_result=""
        max_result=""
        
        # 查找WR开头的结果行
        if grep -q "WR.*R.*R" "$OUTPUT_DIR/linpack_full.log"; then
            echo "[INFO] 找到HPL测试结果..." | tee -a "$LOG_FILE"
            hpl_result=$(grep "WR.*R.*R" "$OUTPUT_DIR/linpack_full.log" | tail -1 | awk '{print $(NF-1)}')
            
            if [ -n "$hpl_result" ]; then
                if echo "$hpl_result" | grep -q "e"; then
                    max_result=$(echo "$hpl_result" | awk '{printf "%.4f", $1}')
                else
                    max_result="$hpl_result"
                fi
                average_result="$max_result"
                echo "[INFO] HPL性能结果: $max_result GFlops" | tee -a "$LOG_FILE"
            fi
        fi
        
        # 如果没找到，尝试其他格式
        if [ -z "$max_result" ]; then
            echo "[INFO] 尝试其他格式提取结果..." | tee -a "$LOG_FILE"
            if grep -qi "gflops" "$OUTPUT_DIR/linpack_full.log"; then
                gflops_line=$(grep -i "gflops" "$OUTPUT_DIR/linpack_full.log" | tail -1)
                if [ -n "$gflops_line" ]; then
                    max_result=$(echo "$gflops_line" | grep -oE '[0-9]+\.?[0-9]*([eE][+-]?[0-9]+)?' | tail -1)
                    if [ -n "$max_result" ]; then
                        max_result=$(echo "$max_result" | awk '{printf "%.4f", $1}')
                        average_result="$max_result"
                    fi
                fi
            fi
        fi
        
        # 设置默认值
        [ -z "$average_result" ] && average_result="0.0"
        [ -z "$max_result" ] && max_result="0.0"
        
        # 保存结果
        echo "Average: $average_result" > "$RESULT_LOG"
        echo "Max: $max_result" >> "$RESULT_LOG"
        
        echo "[INFO] 测试结果 - 平均性能: $average_result GFlops, 最大性能: $max_result GFlops"
        
    else
        echo "[ERROR] HPL测试失败或超时" | tee -a "$LOG_FILE"
        exit 1
    fi
    
else
    echo "[ERROR] 未找到编译好的HPL可执行文件" | tee -a "$LOG_FILE"
    echo "[INFO] 请先运行完整的linpack.sh脚本进行编译" | tee -a "$LOG_FILE"
    exit 1
fi

echo "[INFO] ARM平台Linpack测试完成"
