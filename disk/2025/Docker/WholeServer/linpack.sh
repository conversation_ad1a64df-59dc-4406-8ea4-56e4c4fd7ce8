#!/bin/bash
export LANG=en_US.UTF-8

# 1. 平台检测
ARCH=$(uname -m)
case "$ARCH" in
    x86_64)
        PLATFORM="x86"
        echo "[INFO] 检测到x86_64平台"
        ;;
    aarch64|arm64)
        PLATFORM="arm"
        echo "[INFO] 检测到ARM64平台"
        ;;
    *)
        echo "[ERROR] 不支持的平台: $ARCH"
        exit 1
        ;;
esac

# 2. 目录与变量定义
OUTPUT_DIR="/home/<USER>/linpack"
SOFTWARE_DIR="/home/<USER>"
LINPACK_DIR="/home/<USER>/linpack"
LOG_FILE="$OUTPUT_DIR/linpack_run.log"
RESULT_LOG="$OUTPUT_DIR/linpack.log"
REPORT_MD="$OUTPUT_DIR/linpack_report.md"

# 帮助信息
if [[ "$1" == "-h" || "$1" == "--help" ]]; then
    cat <<EOF
============================================================
脚本名称: linpack.sh

主要功能: 自动化完成Intel Linpack的依赖安装、测试执行及结果输出，适用于容器环境。

平台支持:
  - x86_64: 支持预编译版本，直接下载运行
  - ARM64: 支持源码编译版本，包含鲲鹏平台优化

测试特点:
  - 通过求解大型线性方程组测试CPU浮点运算性能
  - 测试结果以GFLOPS(每秒十亿次浮点运算)表示
  - 可测试CPU多核并行计算能力和内存子系统性能
  - 广泛用于HPC(高性能计算)领域性能评估

用法:
  bash linpack.sh [-h|--help]
参数:
  -h, --help    打印本帮助信息
结果输出:
  - 所有结果、日志、报告均输出到 /home/<USER>/linpack 目录
  - 自动生成 linpack_report.md 测试报告
============================================================
EOF
    exit 0
fi

# 3. 环境检查函数
fix_yum_repos()
{
    echo "[INFO] 检查并修复yum源配置..." | tee -a "$LOG_FILE"

    # 只在有yum的系统上执行
    if ! command -v yum >/dev/null 2>&1; then
        echo "[INFO] 非yum系统，跳过yum源配置" | tee -a "$LOG_FILE"
        return 0
    fi

    # 检测架构
    local arch=$(uname -m)

    if [[ "$arch" == "aarch64" ]]; then
        # ARM平台
        echo "[INFO] 检测到 ARM 架构，使用本地mtos-arm.repo" | tee -a "$LOG_FILE"
        if curl -f -o /etc/yum.repos.d/mtos-arm.repo http://************/huzz/DOCKER/tools/mtos-arm.repo 2>>"$LOG_FILE"; then
            echo "[INFO] ARM yum源配置成功" | tee -a "$LOG_FILE"
        else
            echo "[WARNING] ARM yum源配置失败，将使用默认源" | tee -a "$LOG_FILE"
        fi
    else
        # x86平台
        echo "[INFO] 检测到 x86 架构，使用阿里云CentOS 7源" | tee -a "$LOG_FILE"

        # 备份原有源文件
        if [ -f /etc/yum.repos.d/CentOS-Base.repo ]; then
            cp /etc/yum.repos.d/CentOS-Base.repo /etc/yum.repos.d/CentOS-Base.repo.backup 2>/dev/null
        fi

        if curl -f -o /etc/yum.repos.d/CentOS-Base.repo http://************/huzz/DOCKER/tools/Centos-7.repo 2>>"$LOG_FILE"; then
            echo "[INFO] x86 yum源配置成功" | tee -a "$LOG_FILE"
            # 清理缓存并重建
            yum clean all >/dev/null 2>&1
            yum makecache >/dev/null 2>&1
        else
            echo "[WARNING] 阿里云yum源配置失败，尝试使用清华源" | tee -a "$LOG_FILE"
            if curl -f -o /etc/yum.repos.d/CentOS-Base.repo https://mirrors.tuna.tsinghua.edu.cn/help/centos/ 2>>"$LOG_FILE"; then
                echo "[INFO] 清华yum源配置成功" | tee -a "$LOG_FILE"
                yum clean all >/dev/null 2>&1
                yum makecache >/dev/null 2>&1
            else
                echo "[WARNING] yum源配置失败，将使用默认源" | tee -a "$LOG_FILE"
            fi
        fi
    fi
}

check_dependencies()
{
    echo "[INFO] 检查容器环境依赖..." | tee -a "$LOG_FILE"

    # 检查必要的命令工具
    local missing_tools=()

    for tool in wget tar lscpu grep awk sed curl; do
        if ! command -v "$tool" >/dev/null 2>&1; then
            missing_tools+=("$tool")
        fi
    done

    if [ ${#missing_tools[@]} -gt 0 ]; then
        echo "[ERROR] 缺少必要工具: ${missing_tools[*]}" | tee -a "$LOG_FILE"
        echo "[INFO] 尝试安装缺少的工具..." | tee -a "$LOG_FILE"

        # 先修复yum源（如果是yum系统）
        fix_yum_repos

        # 尝试使用不同的包管理器安装
        if command -v apt-get >/dev/null 2>&1; then
            apt-get update && apt-get install -y "${missing_tools[@]}" util-linux
        elif command -v yum >/dev/null 2>&1; then
            yum install -y "${missing_tools[@]}" util-linux
        elif command -v apk >/dev/null 2>&1; then
            apk add "${missing_tools[@]}" util-linux
        else
            echo "[ERROR] 无法自动安装依赖工具，请手动安装: ${missing_tools[*]}" | tee -a "$LOG_FILE"
            exit 1
        fi
    fi

    echo "[INFO] 依赖检查完成" | tee -a "$LOG_FILE"
}

check_network()
{
    echo "[INFO] 检查网络连接..." | tee -a "$LOG_FILE"

    # 检查是否能访问下载服务器
    if ! wget --spider --timeout=10 --tries=1 http://************/huzz/DOCKER/tools/linpack_onemkl2024.2.tar.gz 2>/dev/null; then
        echo "[WARNING] 无法访问下载服务器 ************，请检查网络连接" | tee -a "$LOG_FILE"
        echo "[INFO] 将在安装阶段重试..." | tee -a "$LOG_FILE"
    else
        echo "[INFO] 网络连接正常" | tee -a "$LOG_FILE"
    fi
}

check_permissions()
{
    echo "[INFO] 检查目录权限..." | tee -a "$LOG_FILE"

    # 检查是否能创建必要目录
    for dir in "$OUTPUT_DIR" "$SOFTWARE_DIR" "/home"; do
        if ! mkdir -p "$dir" 2>/dev/null; then
            echo "[ERROR] 无法创建目录: $dir" | tee -a "$LOG_FILE"
            exit 1
        fi
    done

    # 检查是否有写权限
    if ! touch "$OUTPUT_DIR/test_write" 2>/dev/null; then
        echo "[ERROR] 输出目录无写权限: $OUTPUT_DIR" | tee -a "$LOG_FILE"
        exit 1
    fi
    rm -f "$OUTPUT_DIR/test_write"

    echo "[INFO] 权限检查通过" | tee -a "$LOG_FILE"
}

# 在环境检查函数后添加Intel优化函数
setup_intel_environment()
{
    echo "[INFO] 配置Intel oneAPI环境..." | tee -a "$LOG_FILE"
    
    # 设置oneAPI环境变量
    if [ -f "/opt/intel/oneapi/setvars.sh" ]; then
        source /opt/intel/oneapi/setvars.sh 2>&1 | tee -a "$LOG_FILE"
        echo "[INFO] Intel oneAPI环境已加载" | tee -a "$LOG_FILE"
    else
        echo "[WARNING] 未找到Intel oneAPI环境，使用预编译版本" | tee -a "$LOG_FILE"
    fi
    
    # 启用透明大页
    if [ -w "/sys/kernel/mm/transparent_hugepage/enabled" ]; then
        echo 'always' > /sys/kernel/mm/transparent_hugepage/enabled 2>/dev/null || true
        echo "[INFO] 透明大页已启用" | tee -a "$LOG_FILE"
        cat /sys/kernel/mm/transparent_hugepage/enabled 2>/dev/null | tee -a "$LOG_FILE"
    else
        echo "[WARNING] 无法设置透明大页，可能影响性能" | tee -a "$LOG_FILE"
    fi
}

configure_numa_settings()
{
    echo "[INFO] 配置NUMA优化参数..." | tee -a "$LOG_FILE"
    
    # 检测NUMA节点数和Socket数
    local numa_nodes=$(lscpu | grep "NUMA node(s):" | awk '{print $3}' 2>/dev/null || echo "1")
    local cpu_sockets=$(lscpu | grep "Socket(s):" | awk '{print $2}' 2>/dev/null || echo "1")
    
    # 根据Intel建议设置MPI参数
    export MPI_PROC_NUM=$numa_nodes
    export MPI_PER_NODE=$cpu_sockets
    export NUMA_PER_MPI=1
    
    echo "[INFO] NUMA配置: 节点数=$numa_nodes, Socket数=$cpu_sockets" | tee -a "$LOG_FILE"
    echo "[INFO] MPI配置: PROC_NUM=$MPI_PROC_NUM, PER_NODE=$MPI_PER_NODE, NUMA_PER_MPI=$NUMA_PER_MPI" | tee -a "$LOG_FILE"
}

detect_intel_platform()
{
    local cpu_model=$(lscpu | grep "Model name" | sed 's/Model name: *//g' 2>/dev/null || echo "Unknown")
    local is_ecore=false
    
    # 检测是否为E-core平台（如6766e）
    if echo "$cpu_model" | grep -qi "6766e\|E-core"; then
        is_ecore=true
        echo "[INFO] 检测到Intel E-core平台: $cpu_model" | tee -a "$LOG_FILE"
    else
        echo "[INFO] 检测到Intel P-core平台: $cpu_model" | tee -a "$LOG_FILE"
    fi
    
    echo "$is_ecore"
}

calculate_hpl_parameters()
{
    local is_ecore=$1
    
    # 根据平台类型设置blocking size
    local block_size=384
    if [ "$is_ecore" = "true" ]; then
        block_size=192
        echo "[INFO] E-core平台使用优化的block size: $block_size" | tee -a "$LOG_FILE"
    else
        echo "[INFO] P-core平台使用标准block size: $block_size" | tee -a "$LOG_FILE"
    fi

    # 计算合适的问题规模（基于内存的80%）
    local total_mem_kb=$(grep MemTotal /proc/meminfo | awk '{print $2}' 2>/dev/null || echo "8388608")
    local total_mem_gb=$((total_mem_kb / 1024 / 1024))
    
    # 根据内存大小计算问题规模
    local problem_size
    if [ $total_mem_gb -ge 64 ]; then
        problem_size=210000
    elif [ $total_mem_gb -ge 32 ]; then
        problem_size=150000
    elif [ $total_mem_gb -ge 16 ]; then
        problem_size=100000
    else
        problem_size=80000
    fi
    
    # 设置合理的进程网格（P x Q = MPI_PROC_NUM）
    local p_grid=${MPI_PER_NODE:-2}
    local q_grid=$((${MPI_PROC_NUM:-2} / p_grid))
    
    echo "[INFO] 内存: ${total_mem_gb}GB, 问题规模: N=$problem_size" | tee -a "$LOG_FILE"
    echo "[INFO] 进程网格: P=$p_grid, Q=$q_grid, Block=$block_size" | tee -a "$LOG_FILE"
    
    # 返回参数（通过全局变量）
    HPL_P_GRID=$p_grid
    HPL_Q_GRID=$q_grid
    HPL_BLOCK_SIZE=$block_size
    HPL_PROBLEM_SIZE=$problem_size
}

# 4. 创建必要目录并进行环境检查
mkdir -p "$OUTPUT_DIR" "$SOFTWARE_DIR"
check_dependencies
check_network
check_permissions

linpack_install()
{
    echo "[INFO] 安装Linpack..." | tee -a "$LOG_FILE"
    rm -rf "$LINPACK_DIR"

    if [ "$PLATFORM" = "x86" ]; then
        local download_file="$SOFTWARE_DIR/linpack_onemkl2024.2.tar.gz"
        local local_file="./tool/linpack_onemkl2024.2.tar.gz"

        # 优先使用本地文件
        if [ -f "$local_file" ]; then
            echo "[INFO] 使用本地Linpack工具包..." | tee -a "$LOG_FILE"
            cp "$local_file" "$download_file"
        else
            echo "[INFO] 从网络下载Linpack工具包..." | tee -a "$LOG_FILE"
            local download_url="http://************/huzz/DOCKER/tools/linpack_onemkl2024.2.tar.gz"
            rm -rf "$download_file"

        # 带重试的下载逻辑
        local max_retries=3
        local retry_count=0
        local download_success=false

        while [ $retry_count -lt $max_retries ] && [ "$download_success" = false ]; do
            retry_count=$((retry_count + 1))
            echo "[INFO] 下载尝试 $retry_count/$max_retries..." | tee -a "$LOG_FILE"
            
                if wget --timeout=60 --tries=1 --progress=bar:force "$download_url" -O "$download_file" 2>&1 | tee -a "$LOG_FILE"; then
                    # 验证下载文件
                    if [ -f "$download_file" ] && [ -s "$download_file" ]; then
                            # 验证文件大小是否合理（至少1MB）
                            local file_size=$(stat -c%s "$download_file" 2>/dev/null || echo "0")
                            if [ "$file_size" -gt 1048576 ]; then
                        download_success=true
                                echo "[INFO] 下载成功，文件大小: $file_size 字节" | tee -a "$LOG_FILE"
                            else
                                echo "[WARNING] 下载文件过小，可能不完整" | tee -a "$LOG_FILE"
                                rm -f "$download_file"
                            fi
                    fi
                    fi
                
                if [ "$download_success" = false ] && [ $retry_count -lt $max_retries ]; then
                    echo "[INFO] 等待5秒后重试..." | tee -a "$LOG_FILE"
                    sleep 5
                fi
            done

            if [ "$download_success" = false ]; then
                echo "[ERROR] 下载失败，已重试 $max_retries 次" | tee -a "$LOG_FILE"
                echo "[INFO] 请检查网络连接或手动下载文件到 $local_file" | tee -a "$LOG_FILE"
            exit 1
        fi
        fi

        # 解压文件到 SOFTWARE_DIR
        echo "[INFO] 解压Linpack工具包..." | tee -a "$LOG_FILE"
        if ! tar -zxvf "$download_file" -C "$SOFTWARE_DIR" 2>>"$LOG_FILE"; then
            echo "[ERROR] 解压失败" | tee -a "$LOG_FILE"
        exit 1
    fi

        # 验证解压结果
        if [ ! -d "$LINPACK_DIR" ]; then
            echo "[ERROR] 解压后未找到预期目录: $LINPACK_DIR" | tee -a "$LOG_FILE"
            exit 1
        fi

        # 检查可执行文件
        if [ ! -f "$LINPACK_DIR/runme_xeon64" ]; then
            echo "[ERROR] 未找到可执行文件: $LINPACK_DIR/runme_xeon64" | tee -a "$LOG_FILE"
            exit 1
        fi

        # 设置执行权限
        chmod +x "$LINPACK_DIR/runme_xeon64" 2>>"$LOG_FILE"

        echo "[INFO] x86平台Linpack安装完成" | tee -a "$LOG_FILE"

    elif [ "$PLATFORM" = "arm" ]; then
        echo "[INFO] ARM平台需要编译安装HPL..." | tee -a "$LOG_FILE"
        
        # 检测是否为鲲鹏平台
        local is_kunpeng=false
        if lscpu | grep -qi 'Kunpeng'; then
            is_kunpeng=true
            echo "[INFO] 检测到鲲鹏(Kunpeng)平台，将使用优化配置" | tee -a "$LOG_FILE"
    fi

        # 创建编译目录
        local build_dir="$SOFTWARE_DIR/hpl_build"
        rm -rf "$build_dir"  # 清理之前的编译目录
        mkdir -p "$build_dir"
        cd "$build_dir"

        # 1. 安装编译依赖（包括perl）
        echo "[INFO] 安装编译依赖..." | tee -a "$LOG_FILE"

        # 先修复yum源（如果是yum系统）
        fix_yum_repos

        # 检查并安装必要的编译工具
        local missing_tools=()
        for tool in gcc g++ gfortran make cmake wget tar bzip2 unzip perl; do
            if ! command -v "$tool" >/dev/null 2>&1; then
                missing_tools+=("$tool")
            fi
        done

        if [ ${#missing_tools[@]} -gt 0 ]; then
            echo "[INFO] 需要安装的工具: ${missing_tools[*]}" | tee -a "$LOG_FILE"

            if command -v yum >/dev/null 2>&1; then
                # CentOS/RHEL系统
                yum install -y gcc gcc-c++ gcc-gfortran make cmake wget tar bzip2 unzip perl perl-Data-Dumper
            elif command -v apt-get >/dev/null 2>&1; then
                # Ubuntu/Debian系统
                apt-get update && apt-get install -y gcc g++ gfortran make cmake wget tar bzip2 unzip perl build-essential
            elif command -v dnf >/dev/null 2>&1; then
                # Fedora系统
                dnf install -y gcc gcc-c++ gcc-gfortran make cmake wget tar bzip2 unzip perl
            elif command -v zypper >/dev/null 2>&1; then
                # openSUSE系统
                zypper install -y gcc gcc-c++ gcc-fortran make cmake wget tar bzip2 unzip perl
            else
                echo "[ERROR] 无法识别的包管理器，请手动安装编译工具" | tee -a "$LOG_FILE"
                exit 1
            fi
        else
            echo "[INFO] 所有编译工具已安装" | tee -a "$LOG_FILE"
        fi

        # 验证关键工具是否可用
        for tool in gcc gfortran make; do
            if ! command -v "$tool" >/dev/null 2>&1; then
                echo "[ERROR] 关键工具 $tool 未安装成功" | tee -a "$LOG_FILE"
                exit 1
            fi
        done

        echo "[INFO] 编译环境准备完成" | tee -a "$LOG_FILE"

        # 2. 下载并安装OpenMPI
        echo "[INFO] 下载并编译安装OpenMPI..." | tee -a "$LOG_FILE"
        local openmpi_url="http://************/huzz/DOCKER/tools/openmpi-4.0.7.tar.gz"
        local openmpi_file="$build_dir/openmpi-4.0.7.tar.gz"
        
        if ! wget --timeout=60 "$openmpi_url" -O "$openmpi_file"; then
            echo "[ERROR] 下载OpenMPI失败" | tee -a "$LOG_FILE"
            exit 1
        fi

        if ! tar -zxf "$openmpi_file"; then
            echo "[ERROR] OpenMPI解压失败" | tee -a "$LOG_FILE"
            exit 1
        fi

        cd openmpi-4.0.7 || exit 1
        mkdir -p build
        cd build || exit 1

        # OpenMPI配置参数
        local mpi_prefix="/home/<USER>/OpenMPI"
        rm -rf "$mpi_prefix"  # 清理之前的安装
        mkdir -p "$mpi_prefix"

        echo "[INFO] 配置OpenMPI..." | tee -a "$LOG_FILE"
        if ! ../configure --prefix="$mpi_prefix" \
                    --enable-pretty-print-stacktrace \
                    --enable-orterun-prefix-by-default \
                    --with-cma \
                    --enable-mpi1-compatibility 2>&1 | tee -a "$LOG_FILE"; then
            echo "[ERROR] OpenMPI配置失败" | tee -a "$LOG_FILE"
            exit 1
        fi

        echo "[INFO] 编译OpenMPI（这可能需要几分钟）..." | tee -a "$LOG_FILE"
        if ! make -j"$(nproc)" 2>&1 | tee -a "$LOG_FILE"; then
            echo "[ERROR] OpenMPI编译失败" | tee -a "$LOG_FILE"
            exit 1
        fi

        echo "[INFO] 安装OpenMPI..." | tee -a "$LOG_FILE"
        if ! make install 2>&1 | tee -a "$LOG_FILE"; then
            echo "[ERROR] OpenMPI安装失败" | tee -a "$LOG_FILE"
            exit 1
        fi

        # 设置OpenMPI环境变量
        export MPI_HOME="$mpi_prefix"
        export PATH="$MPI_HOME/bin:$PATH"
        export LD_LIBRARY_PATH="$MPI_HOME/lib:$LD_LIBRARY_PATH"

        # 验证MPI安装
        if ! command -v mpicc >/dev/null 2>&1; then
            echo "[ERROR] mpicc未找到，OpenMPI安装可能失败" | tee -a "$LOG_FILE"
            exit 1
        fi

        echo "[INFO] OpenMPI安装成功，版本: $(mpicc --version | head -1)" | tee -a "$LOG_FILE"

        cd "$build_dir"

        # 3. 下载并安装OpenBLAS或KML
        if [ "$is_kunpeng" = true ]; then
            echo "[INFO] 鲲鹏平台：尝试安装KML库..." | tee -a "$LOG_FILE"
            local kml_url="http://************/huzz/DOCKER/tools/kml-2.4.0-1.aarch64.rpm"
            local kml_file="$build_dir/kml-2.4.0-1.aarch64.rpm"
            
            if wget --timeout=60 "$kml_url" -O "$kml_file"; then
                if command -v rpm >/dev/null 2>&1; then
                    rpm -ivh "$kml_file" 2>&1 | tee -a "$LOG_FILE"
                    if [ $? -eq 0 ]; then
                        export BLAS_LIB="/usr/lib64/kml"
                        export BLAS_FLAGS="-L/usr/lib64/kml -lkml"
                        echo "[INFO] KML库安装成功" | tee -a "$LOG_FILE"
                    else
                        echo "[WARNING] KML安装失败，改用OpenBLAS" | tee -a "$LOG_FILE"
                        is_kunpeng=false
        fi
    else
                    echo "[WARNING] 无rpm命令，改用OpenBLAS" | tee -a "$LOG_FILE"
                    is_kunpeng=false
    fi
    else
                echo "[WARNING] KML下载失败，改用OpenBLAS" | tee -a "$LOG_FILE"
                is_kunpeng=false
    fi
    fi

        if [ "$is_kunpeng" = false ]; then
            echo "[INFO] 下载并编译安装OpenBLAS..." | tee -a "$LOG_FILE"
            local openblas_url="http://************/huzz/DOCKER/tools/OpenBLAS-0.3.6.zip"
            local openblas_file="$build_dir/OpenBLAS-0.3.6.zip"
            
            if ! wget --timeout=60 "$openblas_url" -O "$openblas_file"; then
                echo "[ERROR] 下载OpenBLAS失败" | tee -a "$LOG_FILE"
        exit 1
    fi

            unzip "$openblas_file"
            cd OpenBLAS-0.3.6

            # OpenBLAS编译参数
            make -j$(nproc) TARGET=ARMV8 2>&1 | tee -a "$LOG_FILE"
            if [ $? -ne 0 ]; then
                echo "[ERROR] OpenBLAS编译失败" | tee -a "$LOG_FILE"
        exit 1
    fi

            local openblas_prefix="/home/<USER>/OpenBLAS"
            rm -rf "$openblas_prefix"  # 清理之前的安装
            make install PREFIX="$openblas_prefix" 2>&1 | tee -a "$LOG_FILE"
            if [ $? -ne 0 ]; then
                echo "[ERROR] OpenBLAS安装失败" | tee -a "$LOG_FILE"
                exit 1
    fi

            export BLAS_LIB="$openblas_prefix/lib"
            export BLAS_FLAGS="-L$openblas_prefix/lib -lopenblas"

            echo "[INFO] OpenBLAS安装成功" | tee -a "$LOG_FILE"
            cd "$build_dir"
        fi

        # 4. 下载并编译HPL
        echo "[INFO] 下载并编译HPL..." | tee -a "$LOG_FILE"
        local hpl_url="http://************/huzz/DOCKER/tools/hpl-2.3.tar.gz"
        local hpl_file="$build_dir/hpl-2.3.tar.gz"

        if ! wget --timeout=60 "$hpl_url" -O "$hpl_file"; then
            echo "[ERROR] 下载HPL失败" | tee -a "$LOG_FILE"
            exit 1
        fi

        # 完全清理并重新解压HPL
        echo "[INFO] 清理旧的HPL目录并重新解压..." | tee -a "$LOG_FILE"
        rm -rf hpl-2.3
        tar -zxf "$hpl_file"
        cd hpl-2.3

        # 验证解压结果
        if [ ! -f "Make.top" ] || [ ! -d "src" ]; then
            echo "[ERROR] HPL解压不完整" | tee -a "$LOG_FILE"
            exit 1
        fi

        # 创建HPL配置文件（修复gfortran链接问题）
        local hpl_dir=$(pwd)
        echo "[INFO] 创建HPL配置文件: Make.Linux_ARM" | tee -a "$LOG_FILE"

        # 修复BLAS_FLAGS，添加-lgfortran解决链接问题
        local fixed_blas_flags="$BLAS_FLAGS -lgfortran"

        cat > Make.Linux_ARM << EOF
SHELL        = /bin/sh
CD           = cd
CP           = cp
LN_S         = ln -fs
MKDIR        = mkdir -p
RM           = /bin/rm -f
TOUCH        = touch
ARCH         = Linux_ARM
TOPdir       = $hpl_dir
INCdir       = \$(TOPdir)/include
BINdir       = \$(TOPdir)/bin/\$(ARCH)
LIBdir       = \$(TOPdir)/lib/\$(ARCH)
HPLlib       = \$(LIBdir)/libhpl.a
MPdir        = $MPI_HOME
MPinc        = -I\$(MPdir)/include
MPlib        = -L\$(MPdir)/lib -lmpi
LAdir        = $BLAS_LIB
LAinc        =
LAlib        = $fixed_blas_flags
F2CDEFS      = -DAdd__ -DF77_INTEGER=int -DStringSunStyle
HPL_OPTS     = -DHPL_CALL_CBLAS
HPL_INCLUDES = -I\$(INCdir) -I\$(INCdir)/\$(ARCH) \$(LAinc) \$(MPinc)
HPL_LIBS     = \$(HPLlib) \$(LAlib) \$(MPlib)
HPL_DEFS     = \$(F2CDEFS) \$(HPL_OPTS) \$(HPL_INCLUDES)
CC           = mpicc
CCNOOPT      = \$(HPL_DEFS)
CCFLAGS      = \$(HPL_DEFS) -fomit-frame-pointer -O3 -funroll-loops
LINKER       = mpicc
LINKFLAGS    = \$(CCFLAGS)
ARCHIVER     = ar
ARFLAGS      = r
RANLIB       = echo
EOF

        echo "[INFO] 验证MPI和BLAS环境..." | tee -a "$LOG_FILE"

        # 验证MPI编译器
        if ! mpicc --version >/dev/null 2>&1; then
            echo "[ERROR] mpicc不可用" | tee -a "$LOG_FILE"
            exit 1
        fi

        echo "[INFO] MPI编译器版本: $(mpicc --version | head -1)" | tee -a "$LOG_FILE"

        # 验证BLAS库路径
        if [ ! -d "$BLAS_LIB" ]; then
            echo "[ERROR] BLAS库路径不存在: $BLAS_LIB" | tee -a "$LOG_FILE"
            exit 1
        fi

        # 设置LD_LIBRARY_PATH，确保动态库可用
        export LD_LIBRARY_PATH="$BLAS_LIB:$MPI_HOME/lib:$LD_LIBRARY_PATH"

        # 验证编译环境
        echo "[INFO] 测试MPI编译环境..." | tee -a "$LOG_FILE"
        cat > test_mpi.c << 'EOF'
#include <mpi.h>
#include <stdio.h>
int main(int argc, char** argv) {
    MPI_Init(&argc, &argv);
    printf("MPI test successful\n");
    MPI_Finalize();
    return 0;
}
EOF

        if mpicc -o test_mpi test_mpi.c 2>>"$LOG_FILE"; then
            echo "[INFO] MPI编译测试成功" | tee -a "$LOG_FILE"
            rm -f test_mpi test_mpi.c
        else
            echo "[ERROR] MPI编译测试失败" | tee -a "$LOG_FILE"
            rm -f test_mpi test_mpi.c
            exit 1
        fi

        echo "[INFO] 开始HPL编译（避免无限循环）..." | tee -a "$LOG_FILE"
        
        # 设置编译环境变量
        export MAKEFLAGS="-j$(nproc)"

        echo "[INFO] 开始HPL编译..." | tee -a "$LOG_FILE"

        # 首先尝试清理之前的编译
        echo "[INFO] 清理之前的编译文件..." | tee -a "$LOG_FILE"
        make clean arch=Linux_ARM 2>&1 | tee -a "$LOG_FILE" || true

        # 手动创建必要目录结构
        echo "[INFO] 创建目录结构..." | tee -a "$LOG_FILE"
        mkdir -p bin/Linux_ARM lib/Linux_ARM include/Linux_ARM

        # 为每个子目录创建Make.inc链接
        echo "[INFO] 设置Make.inc链接..." | tee -a "$LOG_FILE"
        find . -type d -name Linux_ARM | while read dir; do
            ln -fs ../../../Make.Linux_ARM "$dir/Make.inc" 2>/dev/null || true
        done

        # 尝试标准编译方式
        echo "[INFO] 尝试标准编译方式..." | tee -a "$LOG_FILE"
        if timeout 1800 make arch=Linux_ARM > build.log 2>&1; then
            echo "[INFO] 标准编译成功" | tee -a "$LOG_FILE"
        else
            echo "[WARNING] 标准编译失败，查看错误信息..." | tee -a "$LOG_FILE"
            if [ -f build.log ]; then
                echo "[DEBUG] 编译错误信息:" | tee -a "$LOG_FILE"
                tail -50 build.log | tee -a "$LOG_FILE"
            fi

            echo "[INFO] 尝试分步编译..." | tee -a "$LOG_FILE"

            # 分步编译各个组件
            for component in src_lib src_test; do
                echo "[INFO] 编译组件: $component..." | tee -a "$LOG_FILE"
                if ! timeout 600 make $component arch=Linux_ARM 2>&1 | tee -a "$LOG_FILE"; then
                    echo "[WARNING] 组件 $component 编译失败" | tee -a "$LOG_FILE"
                fi
            done

            # 如果分步编译也失败，尝试手动编译关键部分
            if [ ! -f "bin/Linux_ARM/xhpl" ]; then
                echo "[INFO] 尝试手动编译关键组件..." | tee -a "$LOG_FILE"

                # 编译核心库
                for subdir in src/auxil src/blas src/comm src/grid src/panel src/pauxil src/pfact src/pgesv; do
                    if [ -d "$subdir" ]; then
                        echo "[INFO] 手动编译 $subdir..." | tee -a "$LOG_FILE"
                        mkdir -p "$subdir/Linux_ARM"
                        cp Make.Linux_ARM "$subdir/Linux_ARM/Make.inc"

                        cd "$subdir/Linux_ARM"
                        if timeout 300 make 2>&1 | tee -a "$LOG_FILE"; then
                            echo "[INFO] $subdir 编译成功" | tee -a "$LOG_FILE"
                        else
                            echo "[WARNING] $subdir 编译失败" | tee -a "$LOG_FILE"
                        fi
                        cd "$hpl_dir"
                    fi
                done

                # 编译测试程序
                for testdir in testing/matgen testing/timer testing/pmatgen testing/ptimer testing/ptest; do
                    if [ -d "$testdir" ]; then
                        echo "[INFO] 手动编译 $testdir..." | tee -a "$LOG_FILE"
                        mkdir -p "$testdir/Linux_ARM"
                        cp Make.Linux_ARM "$testdir/Linux_ARM/Make.inc"

                        cd "$testdir/Linux_ARM"
                        if timeout 300 make 2>&1 | tee -a "$LOG_FILE"; then
                            echo "[INFO] $testdir 编译成功" | tee -a "$LOG_FILE"
                        else
                            echo "[WARNING] $testdir 编译失败" | tee -a "$LOG_FILE"
                        fi
                        cd "$hpl_dir"
                    fi
                done
            fi
        fi

        # 验证编译结果
        echo "[INFO] 检查HPL编译结果..." | tee -a "$LOG_FILE"

        if [ ! -f "bin/Linux_ARM/xhpl" ]; then
            echo "[WARNING] 标准位置未找到xhpl可执行文件" | tee -a "$LOG_FILE"
            echo "[DEBUG] 搜索所有可能的xhpl位置:" | tee -a "$LOG_FILE"
            find . -name "xhpl" -type f 2>/dev/null | tee -a "$LOG_FILE"

            # 尝试查找其他可能的可执行文件位置
            xhpl_found=false
            for possible_path in "testing/ptest/Linux_ARM/xhpl" "bin/Linux_ARM/xhpl" "testing/ptest/xhpl"; do
                if [ -f "$possible_path" ]; then
                    echo "[INFO] 在 $possible_path 找到xhpl" | tee -a "$LOG_FILE"
                    mkdir -p bin/Linux_ARM
                    cp "$possible_path" bin/Linux_ARM/xhpl
                    chmod +x bin/Linux_ARM/xhpl
                    xhpl_found=true
                    break
                fi
            done

            if [ "$xhpl_found" = false ]; then
                echo "[ERROR] 未找到HPL可执行文件，尝试手动链接..." | tee -a "$LOG_FILE"

                # 检查是否有编译的目标文件
                if [ -d "testing/ptest/Linux_ARM" ]; then
                    cd testing/ptest/Linux_ARM || exit 1
                    echo "[INFO] 尝试手动链接xhpl..." | tee -a "$LOG_FILE"

                    # 手动链接
                    if mpicc -o xhpl *.o ../../../lib/Linux_ARM/libhpl.a $BLAS_FLAGS -L$MPI_HOME/lib -lmpi 2>>"$LOG_FILE"; then
                        echo "[INFO] 手动链接成功" | tee -a "$LOG_FILE"
                        mkdir -p ../../../bin/Linux_ARM
                        cp xhpl ../../../bin/Linux_ARM/
                        chmod +x ../../../bin/Linux_ARM/xhpl
                        cd "$hpl_dir"
                    else
                        echo "[ERROR] 手动链接失败" | tee -a "$LOG_FILE"
                        cd "$hpl_dir"
                        exit 1
                    fi
                else
                    echo "[ERROR] 编译目录不存在，HPL编译完全失败" | tee -a "$LOG_FILE"
                    exit 1
                fi
            fi
        else
            echo "[INFO] HPL可执行文件编译成功: bin/Linux_ARM/xhpl" | tee -a "$LOG_FILE"
        fi

        # 创建linpack目录并复制文件
        mkdir -p "$LINPACK_DIR"
        cp bin/Linux_ARM/xhpl "$LINPACK_DIR/"
        chmod +x "$LINPACK_DIR/xhpl"

        echo "[INFO] HPL可执行文件已复制到: $LINPACK_DIR/xhpl" | tee -a "$LOG_FILE"

        # 验证可执行文件
        if ! "$LINPACK_DIR/xhpl" --help >/dev/null 2>&1 && ! ldd "$LINPACK_DIR/xhpl" >/dev/null 2>&1; then
            echo "[WARNING] HPL可执行文件可能有问题，但继续执行" | tee -a "$LOG_FILE"
        fi

        # 创建HPL.dat配置文件（修复单进程配置）
        cat > "$LINPACK_DIR/HPL.dat" << 'EOF'
HPLinpack benchmark input file
Innovative Computing Laboratory, University of Tennessee
HPL.out      output file name (if any)
6            device out (6=stdout,7=stderr,file)
1            # of problems sizes (N)
1000         Ns
1            # of NBs
128          NBs
0            PMAP process mapping (0=Row-,1=Column-major)
1            # of process grids (P x Q)
1            Ps
1            Qs
16.0         threshold
1            # of panel fact
2            PFACTs (0=left, 1=Crout, 2=Right)
1            # of recursive stopping criterium
4            NBMINs (>= 1)
1            # of panels in recursion
2            NDIVs
1            # of recursive panel fact.
2            RFACTs (0=left, 1=Crout, 2=Right)
1            # of broadcast
1            BCASTs (0=1rg,1=1rM,2=2rg,3=2rM,4=Lng,5=LnM)
1            # of lookahead depth
1            DEPTHs (>=0)
2            SWAP (0=bin-exch,1=long,2=mix)
64           swapping threshold
0            L1 in (0=transposed,1=no-transposed) form
0            U  in (0=transposed,1=no-transposed) form
1            Equilibration (0=no,1=yes)
8            memory alignment in double (> 0)
EOF

        # 创建运行脚本（修复单进程运行）
        cat > "$LINPACK_DIR/run_hpl.sh" << EOF
#!/bin/bash
export MPI_HOME=$MPI_HOME
export PATH=\$MPI_HOME/bin:\$PATH
export LD_LIBRARY_PATH=\$MPI_HOME/lib:\$LD_LIBRARY_PATH

# 获取CPU核心数
CORES=\$(nproc)
echo "检测到CPU核心数: \$CORES"

# 使用单进程配置（P=1, Q=1）
P=1
Q=1
echo "使用单进程配置: P=\$P, Q=\$Q"

# 根据内存动态调整问题规模
TOTAL_MEM_KB=\$(grep MemTotal /proc/meminfo | awk '{print \$2}')
TOTAL_MEM_GB=\$((TOTAL_MEM_KB / 1024 / 1024))

if [ \$TOTAL_MEM_GB -ge 64 ]; then
    N=5000
elif [ \$TOTAL_MEM_GB -ge 32 ]; then
    N=3000
elif [ \$TOTAL_MEM_GB -ge 16 ]; then
    N=2000
else
    N=1000
fi

echo "内存: \${TOTAL_MEM_GB}GB, 使用问题规模: N=\$N"

# 更新HPL.dat文件
sed -i "s/^[0-9]*.*Ps$/\$P            Ps/" HPL.dat
sed -i "s/^[0-9]*.*Qs$/\$Q           Qs/" HPL.dat
sed -i "s/^[0-9]*.*Ns$/\$N        Ns/" HPL.dat

# 运行HPL（单进程）
mpirun -np 1 --allow-run-as-root ./xhpl
EOF

        chmod +x "$LINPACK_DIR/run_hpl.sh"
        echo "[INFO] ARM平台HPL编译安装完成" | tee -a "$LOG_FILE"
        echo "[INFO] 可执行文件: $LINPACK_DIR/xhpl" | tee -a "$LOG_FILE"
        echo "[INFO] 运行脚本: $LINPACK_DIR/run_hpl.sh" | tee -a "$LOG_FILE"
    else
        echo "[ERROR] 未知平台: $PLATFORM" | tee -a "$LOG_FILE"
        exit 1
    fi
}

linpack_test()
{
    echo "[INFO] 开始Linpack测试..." | tee -a "$LOG_FILE"
    rm -rf "$RESULT_LOG"

    # 获取CPU信息用于报告
    cpu_model=$(lscpu 2>/dev/null | grep "Model name" | sed 's/Model name: *//g' || echo "Unknown CPU")
    cpu_cores=$(lscpu 2>/dev/null | grep "^CPU(s):" | awk '{print $2}' || echo "Unknown")

    # 如果lscpu不可用，���试其他方法
    if [ "$cpu_model" = "Unknown CPU" ]; then
        cpu_model=$(cat /proc/cpuinfo 2>/dev/null | grep "model name" | head -1 | cut -d: -f2 | sed 's/^ *//' || echo "Unknown CPU")
    fi

    if [ "$cpu_cores" = "Unknown" ]; then
        cpu_cores=$(nproc 2>/dev/null || grep -c ^processor /proc/cpuinfo 2>/dev/null || echo "Unknown")
    fi

    echo "[INFO] 测试平台: $PLATFORM ($cpu_model), $cpu_cores 核心" | tee -a "$LOG_FILE"

    if [ "$PLATFORM" = "x86" ]; then
        echo "[INFO] 执行x86平台Intel优化Linpack测试..." | tee -a "$LOG_FILE"

        # 配置Intel环境
        setup_intel_environment
        configure_numa_settings

        # 检测Intel平台类型
        local is_ecore=$(detect_intel_platform)

        # 计算HPL参数
        calculate_hpl_parameters "$is_ecore"

        # 检查测试目录和可执行文件
        if [ ! -d "$LINPACK_DIR" ]; then
            echo "[ERROR] Linpack目录不存在: $LINPACK_DIR" | tee -a "$LOG_FILE"
            exit 1
        fi

        cd "$LINPACK_DIR" || exit 1

        # 优先使用Intel推荐的运行方式
        local run_script=""
        if [ -f "./runme_intel64_dynamic" ]; then
            run_script="./runme_intel64_dynamic"
            echo "[INFO] 使用Intel优化运行脚本: runme_intel64_dynamic" | tee -a "$LOG_FILE"
        elif [ -f "./runme_xeon64" ]; then
            run_script="./runme_xeon64"
            echo "[INFO] 使用标准运行脚本: runme_xeon64" | tee -a "$LOG_FILE"
        else
            echo "[ERROR] 未找到可执行的运行脚本" | tee -a "$LOG_FILE"
            exit 1
        fi

        if [ ! -x "$run_script" ]; then
            echo "[WARNING] 设置可执行权限..." | tee -a "$LOG_FILE"
            chmod +x "$run_script"
        fi

        # 运行Intel优化版本的测试
        echo "[INFO] 开始执行Intel优化Linpack基准测试，这可能需要几分钟..." | tee -a "$LOG_FILE"
        echo "[INFO] 使用参数: P=$HPL_P_GRID, Q=$HPL_Q_GRID, Block=$HPL_BLOCK_SIZE, N=$HPL_PROBLEM_SIZE" | tee -a "$LOG_FILE"

        # 根据运行脚本类型选择执行方式
        if [[ "$run_script" == *"intel64_dynamic"* ]]; then
            # 使用Intel推荐的参数运行
            if ! timeout 1800 "$run_script" -p "$HPL_P_GRID" -q "$HPL_Q_GRID" -b "$HPL_BLOCK_SIZE" -n "$HPL_PROBLEM_SIZE" > "$OUTPUT_DIR/linpack_full.log" 2>&1; then
                echo "[WARNING] Intel优化参数运行失败，尝试默认参数..." | tee -a "$LOG_FILE"
                timeout 1800 "$run_script" > "$OUTPUT_DIR/linpack_full.log" 2>&1
            fi
        else
            # 使用标准方式运行
            if ! timeout 1800 "$run_script" > "$OUTPUT_DIR/linpack_full.log" 2>&1; then
                echo "[ERROR] Linpack测试执行失败或超时(30分钟)" | tee -a "$LOG_FILE"
                if [ -f "$OUTPUT_DIR/linpack_full.log" ]; then
                    echo "[INFO] 部分测试输出:" | tee -a "$LOG_FILE"
                    tail -20 "$OUTPUT_DIR/linpack_full.log" | tee -a "$LOG_FILE"
                fi
                exit 1
            fi
        fi

        # 检查输出文件
        if [ ! -f "$OUTPUT_DIR/linpack_full.log" ] || [ ! -s "$OUTPUT_DIR/linpack_full.log" ]; then
            echo "[ERROR] 测试输出文件为空或不存在" | tee -a "$LOG_FILE"
            exit 1
        fi

        # 调试：显示测试输出的最后几行
        echo "[DEBUG] 测试输出的最后20行:" | tee -a "$LOG_FILE"
        tail -20 "$OUTPUT_DIR/linpack_full.log" | tee -a "$LOG_FILE"

        # 改进的结果提取逻辑 - 基于实际输出格式
        average_result=""
        max_result=""

        # 查找Performance Summary部分的最高性能数据
        if grep -q "Performance Summary (GFlops)" "$OUTPUT_DIR/linpack_full.log"; then
            echo "[INFO] 找到Performance Summary，提取最佳性能数据..." | tee -a "$LOG_FILE"

            # 提取Performance Summary表格中的最后一行数据（通常是最大问题规模的结果）
            summary_line=$(grep -A 20 "Performance Summary (GFlops)" "$OUTPUT_DIR/linpack_full.log" | grep "^[0-9]" | tail -1)

            if [ -n "$summary_line" ]; then
                echo "[DEBUG] 最佳性能行: $summary_line" | tee -a "$LOG_FILE"
                # 提取Average和Maximal列的值（第4和第5列）
                average_result=$(echo "$summary_line" | awk '{print $4}')
                max_result=$(echo "$summary_line" | awk '{print $5}')
            fi
        fi

        # 如果Performance Summary提取失败，尝试从详细结果中提取最高值
        if [ -z "$average_result" ] || [ -z "$max_result" ]; then
            echo "[INFO] 从详细测试结果中提取最高性能值..." | tee -a "$LOG_FILE"

            # 从所有测试行中提取GFlops列的最高值
            max_result=$(grep "^[0-9].*pass$" "$OUTPUT_DIR/linpack_full.log" | awk '{print $5}' | sort -n | tail -1)

            # 计算平均值（取最后几个大规模测试的平均）
            average_result=$(grep "^[0-9].*pass$" "$OUTPUT_DIR/linpack_full.log" | tail -5 | awk '{sum+=$5; count++} END {if(count>0) print sum/count; else print "0.0"}')
        fi

        # 验证提取的数值格式
        if ! echo "$average_result" | grep -q "^[0-9]*\.[0-9]*$"; then
            echo "[WARNING] Average结果格式异常: $average_result，设置为0.0" | tee -a "$LOG_FILE"
            average_result="0.0"
        fi

        if ! echo "$max_result" | grep -q "^[0-9]*\.[0-9]*$"; then
            echo "[WARNING] Max结果格式异常: $max_result，设置为0.0" | tee -a "$LOG_FILE"
            max_result="0.0"
        fi

        # 如果仍然没有有效结果，设置默认值
        [ -z "$average_result" ] && average_result="0.0"
        [ -z "$max_result" ] && max_result="0.0"

        # 保存结果到日志文件
        echo "Average: $average_result" > "$RESULT_LOG"
        echo "Max: $max_result" >> "$RESULT_LOG"

        echo "[INFO] 平均性能: $average_result GFlops" | tee -a "$LOG_FILE"
        echo "[INFO] 最大性能: $max_result GFlops" | tee -a "$LOG_FILE"
        echo "[INFO] Intel优化配置: E-core=$is_ecore, Block=$HPL_BLOCK_SIZE, Grid=${HPL_P_GRID}x${HPL_Q_GRID}" | tee -a "$LOG_FILE"

    elif [ "$PLATFORM" = "arm" ]; then
        echo "[INFO] 执行ARM平台HPL测试..." | tee -a "$LOG_FILE"

        # 检查测试目录和可执行文件
        if [ ! -d "$LINPACK_DIR" ]; then
            echo "[ERROR] HPL目录不存在: $LINPACK_DIR" | tee -a "$LOG_FILE"
            exit 1
        fi

        cd "$LINPACK_DIR" || exit 1

        if [ ! -f "./xhpl" ]; then
            echo "[ERROR] HPL可执行文件不存在: ./xhpl" | tee -a "$LOG_FILE"
            exit 1
        fi

        if [ ! -f "./run_hpl.sh" ]; then
            echo "[ERROR] HPL运行脚本不存在: ./run_hpl.sh" | tee -a "$LOG_FILE"
            exit 1
        fi

        # 设置MPI环境变量
        export MPI_HOME="/home/<USER>/OpenMPI"
        export PATH="$MPI_HOME/bin:$PATH"
        export LD_LIBRARY_PATH="$MPI_HOME/lib:$LD_LIBRARY_PATH"

        # 运行ARM版本的HPL测试
        echo "[INFO] 开始执行HPL基准测试，这可能需要较长时间..." | tee -a "$LOG_FILE"

        if ! timeout 3600 bash ./run_hpl.sh > "$OUTPUT_DIR/linpack_full.log" 2>&1; then
            echo "[ERROR] HPL测试执行失败或超时(60分钟)" | tee -a "$LOG_FILE"
            if [ -f "$OUTPUT_DIR/linpack_full.log" ]; then
                echo "[INFO] 部分测试输出:" | tee -a "$LOG_FILE"
                tail -20 "$OUTPUT_DIR/linpack_full.log" | tee -a "$LOG_FILE"
            fi
            exit 1
        fi

        # 检查输出文件
        if [ ! -f "$OUTPUT_DIR/linpack_full.log" ] || [ ! -s "$OUTPUT_DIR/linpack_full.log" ]; then
            echo "[ERROR] 测试输出文件为空或不存在" | tee -a "$LOG_FILE"
            exit 1
        fi

        # 调试：显示测试输出的最后几行
        echo "[DEBUG] HPL测试输出的最后20行:" | tee -a "$LOG_FILE"
        tail -20 "$OUTPUT_DIR/linpack_full.log" | tee -a "$LOG_FILE"

        # HPL结果提取逻辑（修复结果提取）
        average_result=""
        max_result=""

        # HPL输出格式通常包含类似这样的行：
        # WR11R2R4         1000   128     1     1               0.03             2.5660e+01
        # 最后一列是GFlops性能数据
        if grep -q "WR.*R.*R" "$OUTPUT_DIR/linpack_full.log"; then
            echo "[INFO] 找到HPL测试结果..." | tee -a "$LOG_FILE"

            # 提取最后一次成功运行的性能数据（倒数第二列是Gflops）
            hpl_result=$(grep "WR.*R.*R" "$OUTPUT_DIR/linpack_full.log" | tail -1 | awk '{print $(NF-1)}')

            if [ -n "$hpl_result" ]; then
                # 处理科学计数法或普通数字
                if echo "$hpl_result" | grep -q "e"; then
                    # 科学计数法转换
                    max_result=$(echo "$hpl_result" | awk '{printf "%.4f", $1}')
                else
                    # 普通数字
                    max_result="$hpl_result"
                fi
                average_result="$max_result"  # HPL通常只有一次运行结果
                echo "[DEBUG] HPL性能结果: $max_result GFlops" | tee -a "$LOG_FILE"
            fi
        fi

        # 如果没有找到标准格式，尝试其他模式
        if [ -z "$max_result" ]; then
            echo "[INFO] 尝试其他格式提取HPL结果..." | tee -a "$LOG_FILE"

            # 查找包含Gflops的行
            if grep -qi "gflops" "$OUTPUT_DIR/linpack_full.log"; then
                gflops_line=$(grep -i "gflops" "$OUTPUT_DIR/linpack_full.log" | tail -1)
                if [ -n "$gflops_line" ]; then
                    # 提取数值（支持科学计数法）
                    max_result=$(echo "$gflops_line" | grep -oE '[0-9]+\.?[0-9]*([eE][+-]?[0-9]+)?' | tail -1)
                    if [ -n "$max_result" ]; then
                        # 转换科学计数法
                        max_result=$(echo "$max_result" | awk '{printf "%.4f", $1}')
                        average_result="$max_result"
                    fi
                fi
            fi
        fi

        # 验证提取的数值格式
        if ! echo "$max_result" | grep -q "^[0-9]*\.[0-9]*$"; then
            echo "[WARNING] HPL结果格式异常: $max_result，设置为0.0" | tee -a "$LOG_FILE"
            max_result="0.0"
        fi

        # 如果仍然没有有效结果，设置默认值
        [ -z "$average_result" ] && average_result="0.0"
        [ -z "$max_result" ] && max_result="0.0"

        # 保存结果到日志文件
        echo "Average: $average_result" > "$RESULT_LOG"
        echo "Max: $max_result" >> "$RESULT_LOG"

        echo "[INFO] 平均性能: $average_result GFlops" | tee -a "$LOG_FILE"
        echo "[INFO] 最大性能: $max_result GFlops" | tee -a "$LOG_FILE"

    else
        echo "[ERROR] 未知平台: $PLATFORM" | tee -a "$LOG_FILE"
        exit 1
    fi

    # 生成MD格式报告
    generate_report "$average_result" "$max_result" "$cpu_model" "$cpu_cores"
    echo "[INFO] Linpack测试完成" | tee -a "$LOG_FILE"
}

generate_report()
{
    local average_gflops=$1
    local max_gflops=$2
    local cpu_model=$3
    local cpu_cores=$4

    echo "# Linpack 测试报告" > "$REPORT_MD"
    echo "" >> "$REPORT_MD"
    echo "## 测试环境" >> "$REPORT_MD"
    echo "- **测试时间**: $(date '+%Y-%m-%d %H:%M:%S')" >> "$REPORT_MD"
    echo "- **测试平台**: $PLATFORM ($ARCH)" >> "$REPORT_MD"
    echo "- **CPU型号**: $cpu_model" >> "$REPORT_MD"
    echo "- **CPU核心数**: $cpu_cores" >> "$REPORT_MD"
    if [ "$PLATFORM" = "x86" ]; then
        echo "- **测试工具**: Intel Linpack Benchmark (OneMKL 2024.2)" >> "$REPORT_MD"
    elif [ "$PLATFORM" = "arm" ]; then
        echo "- **测试工具**: Linpack Benchmark (ARM编译版本)" >> "$REPORT_MD"
    fi
    echo "" >> "$REPORT_MD"

    echo "## 测试结果" >> "$REPORT_MD"
    echo "- **平均性能**: $average_gflops GFlops" >> "$REPORT_MD"
    echo "- **最大性能**: $max_gflops GFlops" >> "$REPORT_MD"
    echo "" >> "$REPORT_MD"

    echo "## 性能摘要表" >> "$REPORT_MD"
    echo '```' >> "$REPORT_MD"
    # 显示Performance Summary部分
    if grep -q "Performance Summary (GFlops)" "$OUTPUT_DIR/linpack_full.log"; then
        grep -A 20 "Performance Summary (GFlops)" "$OUTPUT_DIR/linpack_full.log" | head -20 >> "$REPORT_MD"
    else
        echo "Performance Summary not found in output" >> "$REPORT_MD"
    fi
    echo '```' >> "$REPORT_MD"
    echo "" >> "$REPORT_MD"

    echo "## 测试说明" >> "$REPORT_MD"
    echo "Linpack是一个通过求解线性方程组来测试计算机浮点性能的基准测试工具。" >> "$REPORT_MD"
    echo "" >> "$REPORT_MD"
    echo "- **GFlops值越高表示浮点计算性能越好**" >> "$REPORT_MD"
    echo "- **Average**: 多次测试的平均性能" >> "$REPORT_MD"
    echo "- **Maximal**: 测试过程中达到的最高性能" >> "$REPORT_MD"
    echo "- **结果同时反映了CPU计算能力和内存子系统性能**" >> "$REPORT_MD"
    echo "- **测试规模从1000x1000到45000x45000矩阵**" >> "$REPORT_MD"

    echo "[INFO] 测试报告已生成: $REPORT_MD" | tee -a "$LOG_FILE"
}

callback()
{
    if [ -n "$task_id" ] && [ -n "$domain" ]; then
        echo "[INFO] 回调结果到服务器..." | tee -a "$LOG_FILE"
        cd "$OUTPUT_DIR" || return
        # 获取Max值作为主要性能指标
        indexValue=$(grep "Max:" "$RESULT_LOG" | awk '{print $2}')
        curl -X POST -F "taskId=$task_id" -F 'taskIndexListStr=[{"indexTag":"GFlops","indexValue":"'"$indexValue"'"}]' "$domain/saturn/script/callback"
        echo "[INFO] 回调完成" | tee -a "$LOG_FILE"
    fi
}
# 5. 主流程
main()
{
    echo "[INFO] 开始Linpack性能测试流程..." | tee -a "$LOG_FILE"

    # 执行安装
    if ! linpack_install; then
        echo "[ERROR] Linpack安装失败" | tee -a "$LOG_FILE"
        exit 1
    fi

    # 执行测试
    if ! linpack_test; then
        echo "[ERROR] Linpack测试失败" | tee -a "$LOG_FILE"
        exit 1
    fi

    # 显示完整的测试结果
    echo -e "\n============ Linpack 测试结果 ============"
    if [ -f "$REPORT_MD" ]; then
        # 直接显示报告内容
        cat "$REPORT_MD"
        echo -e "\n=========================================="
        echo "详细日志文件: $LOG_FILE"
        echo "完整测试输出: $OUTPUT_DIR/linpack_full.log"
        echo "测试报告文件: $REPORT_MD"
    else
        echo "[WARNING] 测试报告文件未生成" | tee -a "$LOG_FILE"
    fi

    echo -e "\n[INFO] Linpack性能测试流程完成" | tee -a "$LOG_FILE"
}

# 执行主流程
main



