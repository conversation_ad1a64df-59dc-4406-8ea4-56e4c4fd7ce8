const { chromium } = require('playwright');

(async () => {
  // 1. 启动浏览器
  const browser = await chromium.launch({ headless: false }); // 可见模式
  const context = await browser.newContext({
    storageState: 'auth.json' // 如果需要复用登录态，可先用官方方法保存登录态
  });
  const page = await context.newPage();

  // 2. 打开目标页面
  await page.goto('https://saturn.sankuai.com/page/baseTestTool');

  // 3. 等待 iframe 加载
  const frame = await page.frameLocator('iframe');

  // 4. 在"创建者"输入框中输入"huzezhi"
  await frame.locator('input[name="creator"]').fill('huzezhi');

  // 5. 点击"搜索"按钮
  await frame.getByRole('button', { name: '搜索' }).click();

  // 6. 等待结果刷新（可选：截图）
  await frame.locator('table').waitFor({ state: 'visible', timeout: 10000 });
  await page.screenshot({ path: 'result.png' });

  // 7. 保持浏览器打开（可选）
  await browser.close();
})();