#------------------------------------------------------------------------------
# SPEC CPU(R) 2017 config for gcc/g++/gfortran on Linux Arm systems
#------------------------------------------------------------------------------
#
# Usage: (1) Copy this to a new name
#             cd $SPEC/config
#             cp Example-x.cfg myname.cfg
#        (2) Change items that are marked 'EDIT' (search for it)
#
# SPEC tested this config file with:
#    Compiler version(s):    Various.  See note "Older GCC" below.
#    Operating system(s):    Ubuntu 16.04, CentOS 7.4
#                            Oracle Linux 7.6 / Red Hat Enterprise Linux 7.6 
#    Hardware:               <PERSON><PERSON><PERSON>, <PERSON><PERSON>shot,
#                            Lenovo ThinkSystem HR330A/HR350A
#
# If your system differs, this config file might not work.
# You might find a better config file at https://www.spec.org/cpu2017/results
#
# Note: Older GCC
#
#   Please use the newest GCC that you can. The default version packaged with 
#   your operating system may be very old; look for alternate packages with a
#   newer version. 
#
#   If you have no choice and must use an old version, here is what to expect:
#
#    - "peak" tuning: Several benchmarks will fail at peak tuning if you use
#                     compilers older than GCC 8.  
#                     In that case, please use base only. 
#                     See: https://www.spec.org/cpu2017/Docs/overview.html#Q16
#                          https://www.spec.org/cpu2017/Docs/config.html#tune
#                     Peak tuning is expected to work for all or nearly all 
#                     benchmarks as of GCC 8 or later..  
#                     Exception: 
#                        - See topic "628.pop2_s basepeak", below.
#
#    - "base" tuning: This config file is expected to work for base tuning with 
#                     GCC 4.8.5 or later
#                     Exception: 
#                      - Compilers vintage about 4.9 may need to turn off the
#                        tree vectorizer, by adding to the base OPTIMIZE flags:
#                             -fno-tree-loop-vectorize
#
# Unexpected errors?  Try reducing the optimization level, or try removing: 
#                           -mcpu=native
#
# Compiler issues: Contact your compiler vendor, not SPEC.
# For SPEC help:   https://www.spec.org/cpu2017/Docs/techsupport.html
#------------------------------------------------------------------------------


#--------- Label --------------------------------------------------------------
# Arbitrary string to tag binaries (no spaces allowed)
#                  Two Suggestions: # (1) EDIT this label as you try new ideas.
%ifndef %{label}
%   define label "mytest"           # (2)      Use a label meaningful to *you*.
%endif


#--------- Preprocessor -------------------------------------------------------
%ifndef %{bits}                # EDIT to control 32 or 64 bit compilation.  Or,
%   define  bits        64     #      you can set it on the command line using:
%endif                         #      'runcpu --define bits=nn'

%ifndef %{build_ncpus}         # EDIT to adjust number of simultaneous compiles.
%   define  build_ncpus  8     #      Or, you can set it on the command line:
%endif                         #      'runcpu --define build_ncpus=nn'

# Don't change this part.
%ifdef %{GCC4} 
%   define model        ""    # mabi not present in older GCC 4
%elif %{bits} == 64
%   define model        -mabi=lp64
%elif %{bits} == 32
%   define model        -mabi=ilp32
%else
%   error Please define number of bits - see instructions in config file
%endif
%if %{label} =~ m/ /
%   error Your label "%{label}" contains spaces.  Please try underscores instead.
%endif
%if %{label} !~ m/^[a-zA-Z0-9._-]+$/
%   error Illegal character in label "%{label}".  Please use only alphanumerics, underscore, hyphen, and period.
%endif


#--------- Global Settings ----------------------------------------------------
# For info, see:
#            https://www.spec.org/cpu2017/Docs/config.html#fieldname
#   Example: https://www..spec.org/cpu2017/Docs/config.html#tune

command_add_redirect = 1
flagsurl             = gcc.xml   # ����˵��1�������ֶ��޸�gcc.xml�������Ż�ѡ��˵��
ignore_errors        = 1
iterations           = 1           # ����˵��2����������Ϊ3�������޶�
label                = %{label}-%{bits}
line_width           = 1020
log_line_width       = 1020
makeflags            = --jobs=%{build_ncpus}
mean_anyway          = 1
output_format        = txt,html,cfg,pdf,csv
preenv               = 1
reportable           = 0          # ����˵��3�����Ա���ȥˮӡ�������޶�
tune                 = base       # EDIT if needed: set to "base" for old GCC. 
                                  #      See note "Older GCC" above.
                                  # ����˵��4�����Է���Ϊbase�������޶�
default:                          # ����˵��5�����Ӱ�˵Ĳ����������޶�
submit               = numactl --localalloc --physcpubind=$SPECCOPYNUM $command   # ����˵��5�����Ӱ�˵Ĳ����������޶�

#--------- How Many CPUs? -----------------------------------------------------
# Both SPECrate and SPECspeed can test multiple chips / cores / hw threads
#    - For SPECrate,  you set the number of copies.
#    - For SPECspeed, you set the number of threads.
# See: https://www.spec.org/cpu2017/Docs/system-requirements.html#MultipleCPUs
#
#    q. How many should I set?
#    a. Unknown, you will have to try it and see!
#
# To get you started, some suggestions:
#
#     copies - This config file defaults to testing only 1 copy.   You might
#              try changing it to match the number of cores on your system,
#              or perhaps the number of virtual CPUs as reported by:
#                     grep -c processor /proc/cpuinfo
#              Be sure you have enough memory.  See:
#              https://www.spec.org/cpu2017/Docs/system-requirements.html#memory
#
#     threads - This config file sets a starting point.  You could try raising
#               it.  A higher thread count is much more likely to be useful for
#               fpspeed than for intspeed.
#
intrate,fprate:
   copies           = 128    # EDIT to change number of copies (see above)
                            # ����˵��6�����Ժ���(����CPU��������)�������޶�
intspeed,fpspeed:
   threads          = 128   # EDIT to change number of OpenMP threads (see above)
                            # ����˵��7�������߳����������޶�

#------- Compilers ------------------------------------------------------------
default:
#  EDIT: The parent directory for your compiler.
#        Do not include the trailing /bin/
#        Do not include a trailing slash
#  Examples:
#   1  On a Red Hat system, you said:
#      'yum install devtoolset-9'
#      Use:                 %   define gcc_dir "/opt/rh/devtoolset-9/root/usr"
#
#   2  You built GCC in:                        /disk1/mybuild/gcc-10.1.0/bin/gcc
#      Use:                 %   define gcc_dir "/disk1/mybuild/gcc-10.1.0"
#
#   3  You want:                                /usr/bin/gcc
#      Use:                 %   define gcc_dir "/usr"
#      WARNING: See section "Older GCC" above.
#
%ifndef %{gcc_dir}
%   define  gcc_dir        /usr   # EDIT (see above)          ����˵��8��������·���������޶�
%endif

# EDIT: If your compiler version is GCC 4 (not recommended; see section "Older
#       GCC" above), enable the next line to avoid errors with -mabi
#
#%define GCC4     # EDIT: remove the '#' from column 1 if using GCC 4

# EDIT: If your compiler version is 10 or greater, you must enable the next 
#       line to avoid compile errors for several FP benchmarks
#
%define GCCge10  # EDIT: remove the '#' from column 1 if using GCC 10 or later   ����˵��9�� gcc10+����ѡ��أ������޶�
%define jemalloc_dir   /home/<USER>


# EDIT if needed: the preENV line adds library directories to the runtime
#      path.  You can adjust it, or add lines for other environment variables.
#      See: https://www.spec.org/cpu2017/Docs/config.html#preenv
#      and: https://gcc.gnu.org/onlinedocs/gcc/Environment-Variables.html
#   preENV_LD_LIBRARY_PATH  = %{gcc_dir}/lib64/:%{gcc_dir}/lib/:/lib64    #����˵��11�� �ɵ���ջ��·���������޶�
   preENV_LD_LIBRARY_PATH  = %{jemalloc_dir}/lib:%{gcc_dir}/lib64:%{ENV_LD_LIBRARY_PATH}
   
   EXTRA_CFLAGS     = -w
   EXTRA_LDFLAGS    = -L%{gcc_dir}/lib64 -L%{gcc_dir}/lib -L%{jemalloc_dir}/lib
   EXTRA_LIBS       = -ljemalloc -Wl,-Map,mapfile      
   
  #preENV_LD_LIBRARY_PATH  = %{gcc_dir}/lib64/:%{gcc_dir}/lib/:/lib64:%{ENV_LD_LIBRARY_PATH}
   SPECLANG                = %{gcc_dir}/bin/
   CC                      = $(SPECLANG)gcc     -std=c99   %{model}
   CXX                     = $(SPECLANG)g++     -std=c++03 %{model}
   FC                      = $(SPECLANG)gfortran           %{model}
   # How to say "Show me your version, please"
   CC_VERSION_OPTION       = --version
   CXX_VERSION_OPTION      = --version
   FC_VERSION_OPTION       = --version

default:
%if %{bits} == 64
   sw_base_ptrsize = 64-bit
   sw_peak_ptrsize = 64-bit
%else
   sw_base_ptrsize = 32-bit
   sw_peak_ptrsize = 32-bit
%endif


#--------- Portability --------------------------------------------------------
default:   # data model applies to all benchmarks
%if %{bits} == 32
    # Strongly recommended because at run-time, operations using modern file
    # systems may fail spectacularly and frequently (or, worse, quietly and
    # randomly) if a program does not accommodate 64-bit metadata.
    EXTRA_PORTABILITY = -D_FILE_OFFSET_BITS=64
%else
    EXTRA_PORTABILITY = -DSPEC_LP64
%endif

# Benchmark-specific portability (ordered by last 2 digits of bmark number)

500.perlbench_r,600.perlbench_s:  #lang='C'
%if %{bits} == 32
%   define suffix AARCH32
%else
%   define suffix AARCH64
%endif
   PORTABILITY    = -DSPEC_LINUX_%{suffix}

521.wrf_r,621.wrf_s:  #lang='F,C'
   CPORTABILITY  = -DSPEC_CASE_FLAG
   FPORTABILITY  = -fconvert=big-endian

523.xalancbmk_r,623.xalancbmk_s:  #lang='CXX'
   PORTABILITY   = -DSPEC_LINUX

526.blender_r:  #lang='CXX,C'
   PORTABILITY   = -funsigned-char -DSPEC_LINUX

527.cam4_r,627.cam4_s:  #lang='F,C'
   PORTABILITY   = -DSPEC_CASE_FLAG

628.pop2_s:  #lang='F,C'
   CPORTABILITY   = -DSPEC_CASE_FLAG 
   FPORTABILITY  = -fconvert=big-endian

#----------------------------------------------------------------------
#       GCC workarounds that do not count as PORTABILITY 
#----------------------------------------------------------------------
# The workarounds in this section would not qualify under the SPEC CPU
# PORTABILITY rule.  
#   - In peak, they can be set as needed for individual benchmarks.
#   - In base, individual settings are not allowed; set for whole suite.
# See: 
#     https://www.spec.org/cpu2017/Docs/runrules.html#portability
#     https://www.spec.org/cpu2017/Docs/runrules.html#BaseFlags
#
# Integer workarounds - peak
#
   500.perlbench_r,600.perlbench_s=peak:    # https://www.spec.org/cpu2017/Docs/benchmarks/500.perlbench_r.html
      EXTRA_CFLAGS = -fno-strict-aliasing -fno-unsafe-math-optimizations -fno-finite-math-only  
   502.gcc_r,602.gcc_s=peak:                # https://www.spec.org/cpu2017/Docs/benchmarks/502.gcc_r.html
      EXTRA_CFLAGS = -fno-strict-aliasing -fgnu89-inline
   505.mcf_r,605.mcf_s=peak:                # https://www.spec.org/cpu2017/Docs/benchmarks/505.mcf_r.html
      EXTRA_CFLAGS = -fno-strict-aliasing 
   525.x264_r,625.x264_s=peak:              # https://www.spec.org/cpu2017/Docs/benchmarks/525.x264_r.html
      EXTRA_CFLAGS = -fcommon
#
# Integer workarounds - base - combine the above - https://www.spec.org/cpu2017/Docs/runrules.html#BaseFlags
#
   intrate,intspeed=base:  
      EXTRA_CFLAGS = -fno-strict-aliasing -fno-unsafe-math-optimizations -fno-finite-math-only -fgnu89-inline -fcommon
#
# Floating Point workarounds - peak
#
   511.povray_r=peak:                       # https://www.spec.org/cpu2017/Docs/benchmarks/511.povray_r.html
      EXTRA_CFLAGS = -fno-strict-aliasing 
   521.wrf_r,621.wrf_s=peak:                # https://www.spec.org/cpu2017/Docs/benchmarks/521.wrf_r.html
%     ifdef %{GCCge10}                      # workaround for GCC v10 (and presumably later)
         EXTRA_FFLAGS = -fallow-argument-mismatch
%     endif
   527.cam4_r,627.cam4_s=peak:              # https://www.spec.org/cpu2017/Docs/benchmarks/527.cam4_r.html
      EXTRA_CFLAGS = -fno-strict-aliasing 
%     ifdef %{GCCge10}                      # workaround for GCC v10 (and presumably later)
         EXTRA_FFLAGS = -fallow-argument-mismatch
%     endif
   # See also topic "628.pop2_s basepeak" below
   628.pop2_s=peak:                         # https://www.spec.org/cpu2017/Docs/benchmarks/628.pop2_s.html
%     ifdef %{GCCge10}                      # workaround for GCC v10 (and presumably later)
         EXTRA_FFLAGS = -fallow-argument-mismatch
%     endif
#
# FP workarounds - base - combine the above - https://www.spec.org/cpu2017/Docs/runrules.html#BaseFlags
#
   fprate,fpspeed=base:    
      EXTRA_CFLAGS = -fno-strict-aliasing 
%     ifdef %{GCCge10}                      # workaround for GCC v10 (and presumably later)
         EXTRA_FFLAGS = -fallow-argument-mismatch
%     endif


#-------- Tuning Flags common to Base and Peak --------------------------------
#
# Speed (OpenMP and Autopar allowed)
#
%if %{bits} == 32
   intspeed,fpspeed:
   #
   # Many of the speed benchmarks (6nn.benchmark_s) do not fit in 32 bits
   # If you wish to run SPECint2017_speed or SPECfp2017_speed, please use
   #
   #     runcpu --define bits=64
   #
   fail_build = 1
%else
   intspeed,fpspeed:
      EXTRA_OPTIMIZE = -fopenmp -DSPEC_OPENMP
   fpspeed:
      #
      # 627.cam4 needs a big stack; the preENV will apply it to all
      # benchmarks in the set, as required by the rules.
      #
      preENV_OMP_STACKSIZE = 120M          # ����˵��12������ջ�����������޶�
      preENV_MALLOC_CONF   = retain:true   # ����˵��13�����ӵ����ڴ���价�������������޶�
%endif

#--------  Base Tuning Flags ----------------------------------------------
# EDIT if needed -- If you run into errors, you may need to adjust the
#                   optimization - for example you may need to remove
#                   the -mcpu=native.   See topic "Older GCC" above.
#
#default=base:         # flags for all base
#  OPTIMIZE         = -g -O3 -mcpu=native

intrate,intspeed=base:         # flags for all base
   OPTIMIZE         = -g -O3 -mcpu=neoverse-n1 -funroll-loops -flto=32
   COPTIMIZE        = --param early-inlining-insns=96  --param max-inline-insns-auto=64  --param inline-unit-growth=96
   CXXOPTIMIZE      = --param early-inlining-insns=256 --param max-inline-insns-auto=128 --param inline-unit-growth=256 -ffinite-loops
   FOPTIMIZE        = --param ipa-cp-eval-threshold=1  --param ipa-cp-unit-growth=80     --param ipa-cp-max-recursive-depth=8 -fno-inline-functions-called-once -fstack-arrays -flto-partition=one

fprate,fpspeed=base:
   OPTIMIZE         = -g -Ofast -mcpu=neoverse-n1 -flto=32
   FOPTIMIZE        = -fno-stack-arrays   
   

#--------  Peak Tuning Flags ----------------------------------------------
default=peak:
   OPTIMIZE         = -g -Ofast -flto -mcpu=native
   PASS1_FLAGS      = -fprofile-generate
   PASS2_FLAGS      = -fprofile-use

# 628.pop2_s basepeak: Depending on the interplay of several optimizations,
#            628.pop2_s might not validate with peak tuning.  Use the base 
#            version instead.  See: 
#            https:// www.spec.org/cpu2017/Docs/benchmarks/628.pop2_s.html
628.pop2_s=peak:  
   basepeak         = yes


#------------------------------------------------------------------------------
# Tester and System Descriptions - EDIT all sections below this point
#------------------------------------------------------------------------------
#   For info about any field, see
#             https://www.spec.org/cpu2017/Docs/config.html#fieldname
#   Example:  https://www.spec.org/cpu2017/Docs/config.html#hw_memory
#-------------------------------------------------------------------------------

#--------- EDIT to match your version -----------------------------------------
default:
   sw_compiler001   = C/C++/Fortran: Version 10. of GCC, the
   sw_compiler002   = GNU Compiler Collection

#--------- EDIT info about you ------------------------------------------------
# To understand the difference between hw_vendor/sponsor/tester, see:
#     https://www.spec.org/cpu2017/Docs/config.html#test_sponsor
intrate,intspeed,fprate,fpspeed: # Important: keep this line
   hw_vendor          = My Corporation
   tester             = My Corporation
   test_sponsor       = My Corporation
   license_num        = nnn (Your SPEC license number)
#  prepared_by        = # Ima Pseudonym                       # Whatever you like: is never output


#--------- EDIT system availability dates -------------------------------------
intrate,intspeed,fprate,fpspeed: # Important: keep this line
                        # Example                             # Brief info about field
   hw_avail           = # Nov-2099                            # Date of LAST hardware component to ship
   sw_avail           = # Nov-2099                            # Date of LAST software component to ship
   fw_bios            = # Version Mumble released May-2099    # Firmware information

#--------- EDIT system information --------------------------------------------
intrate,intspeed,fprate,fpspeed: # Important: keep this line
                        # Example                             # Brief info about field
 # hw_cpu_name        = # Intel Xeon E9-9999 v9               # chip name
   hw_cpu_nominal_mhz = # 9999                                # Nominal chip frequency, in MHz
   hw_cpu_max_mhz     = # 9999                                # Max chip frequency, in MHz
 # hw_disk            = # 9 x 9 TB SATA III 9999 RPM          # Size, type, other perf-relevant info
   hw_model           = # TurboBlaster 3000                   # system model name
 # hw_nchips          = # 99                                  # number chips enabled
   hw_ncores          = # 9999                                # number cores enabled
   hw_ncpuorder       = # 1-9 chips                           # Ordering options
   hw_nthreadspercore = # 9                                   # number threads enabled per core
   hw_other           = # TurboNUMA Router 10 Gb              # Other perf-relevant hw, or "None"

#  hw_memory001       = # 999 GB (99 x 9 GB 2Rx4 PC4-2133P-R, # The 'PCn-etc' is from the JEDEC
#  hw_memory002       = # running at 1600 MHz)                # label on the DIMM.

   hw_pcache          = # 99 KB I + 99 KB D on chip per core  # Primary cache size, type, location
   hw_scache          = # 99 KB I+D on chip per 9 cores       # Second cache or "None"
   hw_tcache          = # 9 MB I+D on chip per chip           # Third  cache or "None"
   hw_ocache          = # 9 GB I+D off chip per system board  # Other cache or "None"

 # sw_file            = # ext99                               # File system
 # sw_os001           = # Linux Sailboat                      # Operating system
 # sw_os002           = # Distribution 7.2 SP1                # and version
   sw_other           = # TurboHeap Library V8.1              # Other perf-relevant sw, or "None"
 # sw_state           = # Run level 99                        # Software state.

   power_management   = # briefly summarize power settings 

# Note: Some commented-out fields above are automatically set to preliminary
# values by sysinfo
#       https://www.spec.org/cpu2017/Docs/config.html#sysinfo
# Uncomment lines for which you already know a better answer than sysinfo
