#!/bin/bash
export LANG=en_US.UTF-8

# 1. 平台检测
ARCH=$(uname -m)
case "$ARCH" in
    x86_64)
        PLATFORM="x86"
        echo "[INFO] 检测到x86_64平台"
        ;;
    aarch64|arm64)
        PLATFORM="arm"
        echo "[INFO] 检测到ARM64平台"
        ;;
    *)
        echo "[ERROR] 不支持的平台: $ARCH"
        exit 1
        ;;
esac

# 2. 目录与变量定义
OUTPUT_DIR="/home/<USER>/stream"
SOFTWARE_DIR="/home/<USER>/stream"
LOG_FILE="$OUTPUT_DIR/stream_run.log"
RESULT_LOG="$OUTPUT_DIR/stream.log"
REPORT_MD="$OUTPUT_DIR/stream_report.md"

# 帮助信息
if [[ "$1" == "-h" || "$1" == "--help" ]]; then
    cat <<EOF
============================================================
脚本名称: stream_test.sh

主要功能: 自动化完成STREAM内存带宽测试的依赖安装、测试执行及结果输出，适用于容器环境。

测试特点:
  - 测试内存子系统的可持续带宽性能
  - 包含Copy、Scale、Add、Triad四种内存操作模式
  - 支持单线程和多线程(OpenMP)两种测试模式
  - 测试结果以MB/s表示内存带宽性能
  - 广泛用于评估系统内存性能和多核扩展性

用法:
  bash stream_test.sh [test_count] [-h|--help]

参数:
  test_count    测试轮数，默认为3轮
  -h, --help    打印本帮助信息

结果输出:
  - 所有结果、日志、报告均输出到 /home/<USER>/stream 目录
  - 自动生成 stream_report.md 测试报告
============================================================
EOF
    exit 0
fi

# 3. 环境检查函数
fix_yum_repos()
{
    echo "[INFO] 检查并修复yum源配置..." | tee -a "$LOG_FILE"

    # 只在有yum的系统上执行
    if ! command -v yum >/dev/null 2>&1; then
        echo "[INFO] 非yum系统，跳过yum源配置" | tee -a "$LOG_FILE"
        return 0
    fi

    # 检测架构
    local arch=$(uname -m)

    # 强制重置yum源
    echo "[INFO] 重置yum源..." | tee -a "$LOG_FILE"
    rm -rf /etc/yum.repos.d/*

    if [[ "$arch" == "aarch64" ]]; then
        # ARM平台
        echo "[INFO] 检测到 ARM 架构，使用本地mtos-arm.repo" | tee -a "$LOG_FILE"
        if curl -f -o /etc/yum.repos.d/mtos-arm.repo http://10.8.104.100/huzz/DOCKER/tools/mtos-arm.repo 2>>"$LOG_FILE"; then
            echo "[INFO] ARM yum源配置成功" | tee -a "$LOG_FILE"
        else
            echo "[WARNING] ARM yum源配置失败，将使用默认源" | tee -a "$LOG_FILE"
        fi
    else
        # x86平台
        echo "[INFO] 检测到 x86 架构，使用阿里云CentOS 7源" | tee -a "$LOG_FILE"
        if curl -f -o /etc/yum.repos.d/CentOS-Base.repo http://10.8.104.100/huzz/DOCKER/tools/Centos-7.repo 2>>"$LOG_FILE"; then
            echo "[INFO] x86 yum源配置成功" | tee -a "$LOG_FILE"
        else
            echo "[WARNING] yum源配置失败，将使用默认源" | tee -a "$LOG_FILE"
        fi
    fi

    yum clean all >/dev/null 2>&1
    yum makecache >/dev/null 2>&1
}

check_dependencies()
{
    echo "[INFO] 检查容器环境依赖..." | tee -a "$LOG_FILE"

    # 检查必要的命令工具
    local missing_tools=()

    for tool in wget gcc make tar lscpu grep awk sed curl; do
        if ! command -v "$tool" >/dev/null 2>&1; then
            missing_tools+=("$tool")
        fi
    done

    if [ ${#missing_tools[@]} -gt 0 ]; then
        echo "[ERROR] 缺少必要工具: ${missing_tools[*]}" | tee -a "$LOG_FILE"
        echo "[INFO] 尝试安装缺少的工具..." | tee -a "$LOG_FILE"

        # 先修复yum源
        fix_yum_repos

        # 尝试使用不同的包管理器安装
        if command -v apt-get >/dev/null 2>&1; then
            apt-get update && apt-get install -y "${missing_tools[@]}" gcc-multilib libc6-dev
        elif command -v yum >/dev/null 2>&1; then
            yum install -y "${missing_tools[@]}" gcc-c++ glibc-devel
        elif command -v apk >/dev/null 2>&1; then
            apk add "${missing_tools[@]}" gcc musl-dev
        else
            echo "[ERROR] 无法自动安装依赖工具，请手动安装: ${missing_tools[*]}" | tee -a "$LOG_FILE"
            exit 1
        fi
    fi

    echo "[INFO] 依赖检查完成" | tee -a "$LOG_FILE"
}

# 4. 创建必要目录并进行环境检查
mkdir -p "$OUTPUT_DIR" "$SOFTWARE_DIR"
check_dependencies

# 5. 获取测试参数
para=${1:-3}  # 默认测试3轮
if ! [[ "$para" =~ ^[0-9]+$ ]] || [ "$para" -lt 1 ]; then
    echo "[WARNING] 无效的测试轮数参数: $para，使用默认值3" | tee -a "$LOG_FILE"
    para=3
fi

echo "[INFO] 设置测试轮数: $para" | tee -a "$LOG_FILE"

# 6. 安装函数
stream_install()
{
    echo "[INFO] 安装STREAM测试工具..." | tee -a "$LOG_FILE"
    rm -rf "$SOFTWARE_DIR"
    mkdir -p "$SOFTWARE_DIR"
    cd "$SOFTWARE_DIR"

    # 先修复yum源并安装编译依赖
    fix_yum_repos

    echo "[INFO] 安装编译依赖..." | tee -a "$LOG_FILE"
    if command -v yum >/dev/null 2>&1; then
        yum install -y gcc gcc-c++ make wget curl
    elif command -v apt-get >/dev/null 2>&1; then
        apt-get update && apt-get install -y gcc g++ make wget curl
    fi

    # 检查OpenMP支持
    echo "[INFO] 检查OpenMP支持..." | tee -a "$LOG_FILE"
    if gcc -fopenmp -dumpversion >/dev/null 2>&1; then
        echo "[INFO] OpenMP支持正常" | tee -a "$LOG_FILE"
        OPENMP_SUPPORT=true
    else
        echo "[WARNING] OpenMP支持异常，将只编译单线程版本" | tee -a "$LOG_FILE"
        OPENMP_SUPPORT=false
    fi

    # 下载STREAM.tar包
    echo "[INFO] 下载STREAM工具包..." | tee -a "$LOG_FILE"
    local download_file="$SOFTWARE_DIR/STREAM.tar"
    local download_url="http://10.8.104.100/huzz/DOCKER/tools/STREAM.tar"
    
    # 带重试的下载逻辑
    local max_retries=3
    local retry_count=0
    local download_success=false

    while [ $retry_count -lt $max_retries ] && [ "$download_success" = false ]; do
        retry_count=$((retry_count + 1))
        echo "[INFO] 下载尝试 $retry_count/$max_retries..." | tee -a "$LOG_FILE"
        
        if wget --timeout=60 --tries=1 --progress=bar:force "$download_url" -O "$download_file" 2>&1 | tee -a "$LOG_FILE"; then
            # 验证下载文件
            if [ -f "$download_file" ] && [ -s "$download_file" ]; then
                # 验证文件大小是否合理（至少1KB）
                local file_size=$(stat -c%s "$download_file" 2>/dev/null || echo "0")
                if [ "$file_size" -gt 1024 ]; then
                    download_success=true
                    echo "[INFO] 下载成功，文件大小: $file_size 字节" | tee -a "$LOG_FILE"
    else
                    echo "[WARNING] 下载文件过小，可能不完整" | tee -a "$LOG_FILE"
                    rm -f "$download_file"
    fi
    fi
    fi

        if [ "$download_success" = false ] && [ $retry_count -lt $max_retries ]; then
            echo "[INFO] 等待5秒后重试..." | tee -a "$LOG_FILE"
            sleep 5
        fi
    done

    if [ "$download_success" = false ]; then
        echo "[ERROR] 下载失败，已重试 $max_retries 次" | tee -a "$LOG_FILE"
        exit 1
    fi

    # 解压STREAM.tar包
    echo "[INFO] 解压STREAM工具包..." | tee -a "$LOG_FILE"
    if ! tar -xf "$download_file" -C "$SOFTWARE_DIR" 2>>"$LOG_FILE"; then
        echo "[ERROR] 解压失败" | tee -a "$LOG_FILE"
        exit 1
    fi

    # 查找stream.c文件
    local stream_c_file=""
    stream_c_file=$(find "$SOFTWARE_DIR" -name "stream.c" -type f | head -1)
    
    if [ -z "$stream_c_file" ]; then
        echo "[ERROR] 解压后未找到stream.c文件" | tee -a "$LOG_FILE"
        echo "[DEBUG] 当前目录内容:" | tee -a "$LOG_FILE"
        ls -la "$SOFTWARE_DIR" | tee -a "$LOG_FILE"
        exit 1
    fi

    echo "[INFO] 找到stream.c文件: $stream_c_file" | tee -a "$LOG_FILE"

    # 如果stream.c不在根目录，复制到根目录
    if [ "$stream_c_file" != "$SOFTWARE_DIR/stream.c" ]; then
        cp "$stream_c_file" "$SOFTWARE_DIR/stream.c"
        echo "[INFO] 已复制stream.c到工作目录" | tee -a "$LOG_FILE"
    fi

    # 验证stream.c文件
    if [ ! -f "$SOFTWARE_DIR/stream.c" ] || [ ! -s "$SOFTWARE_DIR/stream.c" ]; then
        echo "[ERROR] stream.c文件无效" | tee -a "$LOG_FILE"
        exit 1
    fi

    echo "[INFO] STREAM源码准备完成" | tee -a "$LOG_FILE"

    # 获取系统信息用于优化编译参数
    local cpu_cores=$(nproc 2>/dev/null || echo "4")
    local total_mem_kb=$(grep MemTotal /proc/meminfo 2>/dev/null | awk '{print $2}' || echo "4194304")
    local total_mem_gb=$((total_mem_kb / 1024 / 1024))

    # 根据内存大小动态设置数组大小
    local array_size
    if [ "$total_mem_gb" -ge 32 ]; then
        array_size=536870920  # ~16GB内存使用
    elif [ "$total_mem_gb" -ge 16 ]; then
        array_size=268435456  # ~8GB内存使用
    elif [ "$total_mem_gb" -ge 8 ]; then
        array_size=134217728  # ~4GB内存使用
    else
        array_size=67108864   # ~2GB内存使用
    fi

    echo "[INFO] 系统内存: ${total_mem_gb}GB，设置数组大小: $array_size" | tee -a "$LOG_FILE"

    # 编译优化参数
    local base_flags="-O3 -DSTREAM_ARRAY_SIZE=$array_size -DNTIMES=30"
    
    # 平台特定优化
    if [ "$PLATFORM" = "x86" ]; then
        local opt_flags="-march=native -mcmodel=medium -mtune=native"
    else
        local opt_flags="-march=native -mcmodel=small"
    fi

    # 编译单线程版本
    echo "[INFO] 编译单线程版本..." | tee -a "$LOG_FILE"
    if ! gcc $base_flags $opt_flags stream.c -o stream_single 2>>"$LOG_FILE"; then
        echo "[ERROR] 单线程版本编译失败" | tee -a "$LOG_FILE"
        exit 1
    fi

    # 编译多线程版本（如果支持OpenMP）
    if [ "$OPENMP_SUPPORT" = true ]; then
        echo "[INFO] 编译多线程版本..." | tee -a "$LOG_FILE"
        if ! gcc $base_flags $opt_flags -fopenmp stream.c -o stream_omp 2>>"$LOG_FILE"; then
            echo "[WARNING] 多线程版本编译失败，将只使用单线程版本" | tee -a "$LOG_FILE"
            OPENMP_SUPPORT=false
        fi
    fi

    # 设置执行权限
    chmod +x stream_single
    [ "$OPENMP_SUPPORT" = true ] && chmod +x stream_omp

    # 验证编译结果
    if [ ! -x "stream_single" ]; then
        echo "[ERROR] 单线程可执行文件无效" | tee -a "$LOG_FILE"
        exit 1
    fi

    if [ "$OPENMP_SUPPORT" = true ] && [ ! -x "stream_omp" ]; then
        echo "[WARNING] 多线程可执行文件无效" | tee -a "$LOG_FILE"
        OPENMP_SUPPORT=false
    fi

    # 清理下载的tar包
    rm -f "$download_file"

    echo "[INFO] STREAM编译完成" | tee -a "$LOG_FILE"
    echo "[INFO] 单线程版本: stream_single" | tee -a "$LOG_FILE"
    [ "$OPENMP_SUPPORT" = true ] && echo "[INFO] 多线程版本: stream_omp" | tee -a "$LOG_FILE"
}

# 7. 测试函数
stream_test()
{
    echo "[INFO] 开始STREAM内存带宽测试..." | tee -a "$LOG_FILE"
    
    # 清理之前的结果
    rm -rf "$OUTPUT_DIR"/stream_*.log
    cd "$SOFTWARE_DIR"

    # 获取系统信息
    local cpu_model=$(lscpu 2>/dev/null | grep "Model name" | sed 's/Model name: *//g' || echo "Unknown CPU")
    local cpu_cores=$(nproc 2>/dev/null || echo "Unknown")
    local total_mem_kb=$(grep MemTotal /proc/meminfo 2>/dev/null | awk '{print $2}' || echo "Unknown")
    local total_mem_gb=$((total_mem_kb / 1024 / 1024))

    echo "[INFO] 测试环境: $cpu_model, $cpu_cores 核心, ${total_mem_gb}GB 内存" | tee -a "$LOG_FILE"

    local start_time=$(date +%s)

    # 单线程测试
    echo "[INFO] 执行单线程STREAM测试 ($para 轮)..." | tee -a "$LOG_FILE"
    for i in $(seq 1 $para); do
        echo "[INFO] 单线程测试第 $i/$para 轮..." | tee -a "$LOG_FILE"
        echo "=== Single Thread Test $i/$para ===" >> "$OUTPUT_DIR/stream_single.log"
        if ! timeout 300 ./stream_single 2>&1 | grep -A7 "precision of" | grep -v precision >> "$OUTPUT_DIR/stream_single.log"; then
            echo "[WARNING] 单线程测试第 $i 轮超时或失败" | tee -a "$LOG_FILE"
        fi
        echo "" >> "$OUTPUT_DIR/stream_single.log"
    done

    # 多线程测试（如果支持）
    if [ "$OPENMP_SUPPORT" = true ]; then
        echo "[INFO] 执行多线程STREAM测试 ($para 轮)..." | tee -a "$LOG_FILE"
        
        # 设置OpenMP线程数为CPU核心数
        export OMP_NUM_THREADS=$cpu_cores
        echo "[INFO] 设置OpenMP线程数: $OMP_NUM_THREADS" | tee -a "$LOG_FILE"
        
        for i in $(seq 1 $para); do
            echo "[INFO] 多线程测试第 $i/$para 轮..." | tee -a "$LOG_FILE"
            echo "=== Multi Thread Test $i/$para (Threads: $OMP_NUM_THREADS) ===" >> "$OUTPUT_DIR/stream_omp.log"
            if ! timeout 300 ./stream_omp 2>&1 | grep -A7 "precision of" | grep -v precision >> "$OUTPUT_DIR/stream_omp.log"; then
                echo "[WARNING] 多线程测试第 $i 轮超时或失败" | tee -a "$LOG_FILE"
            fi
            echo "" >> "$OUTPUT_DIR/stream_omp.log"
        done
    else
        echo "[INFO] 跳过多线程测试（OpenMP不支持）" | tee -a "$LOG_FILE"
    fi

    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    echo "[INFO] 测试完成，耗时: ${duration}秒" | tee -a "$LOG_FILE"

    # 分析结果
    analyze_results "$cpu_model" "$cpu_cores" "$total_mem_gb"
}

# 8. 结果分析函数
analyze_results()
{
    local cpu_model="$1"
    local cpu_cores="$2"
    local total_mem_gb="$3"

    echo "[INFO] 分析测试结果..." | tee -a "$LOG_FILE"

    # 分析单线程结果
    local single_copy_avg=0 single_scale_avg=0 single_add_avg=0 single_triad_avg=0
    local single_copy_max=0 single_scale_max=0 single_add_max=0 single_triad_max=0

    if [ -f "$OUTPUT_DIR/stream_single.log" ]; then
        single_copy_avg=$(grep "Copy:" "$OUTPUT_DIR/stream_single.log" | awk '{sum+=$2; count++} END {if(count>0) printf "%.1f", sum/count; else print "0.0"}')
        single_scale_avg=$(grep "Scale:" "$OUTPUT_DIR/stream_single.log" | awk '{sum+=$2; count++} END {if(count>0) printf "%.1f", sum/count; else print "0.0"}')
        single_add_avg=$(grep "Add:" "$OUTPUT_DIR/stream_single.log" | awk '{sum+=$2; count++} END {if(count>0) printf "%.1f", sum/count; else print "0.0"}')
        single_triad_avg=$(grep "Triad:" "$OUTPUT_DIR/stream_single.log" | awk '{sum+=$2; count++} END {if(count>0) printf "%.1f", sum/count; else print "0.0"}')

        single_copy_max=$(grep "Copy:" "$OUTPUT_DIR/stream_single.log" | awk '{if($2>max) max=$2} END {printf "%.1f", max+0}')
        single_scale_max=$(grep "Scale:" "$OUTPUT_DIR/stream_single.log" | awk '{if($2>max) max=$2} END {printf "%.1f", max+0}')
        single_add_max=$(grep "Add:" "$OUTPUT_DIR/stream_single.log" | awk '{if($2>max) max=$2} END {printf "%.1f", max+0}')
        single_triad_max=$(grep "Triad:" "$OUTPUT_DIR/stream_single.log" | awk '{if($2>max) max=$2} END {printf "%.1f", max+0}')
    fi

    # 分析多线程结果
    local omp_copy_avg=0 omp_scale_avg=0 omp_add_avg=0 omp_triad_avg=0
    local omp_copy_max=0 omp_scale_max=0 omp_add_max=0 omp_triad_max=0

    if [ "$OPENMP_SUPPORT" = true ] && [ -f "$OUTPUT_DIR/stream_omp.log" ]; then
        omp_copy_avg=$(grep "Copy:" "$OUTPUT_DIR/stream_omp.log" | awk '{sum+=$2; count++} END {if(count>0) printf "%.1f", sum/count; else print "0.0"}')
        omp_scale_avg=$(grep "Scale:" "$OUTPUT_DIR/stream_omp.log" | awk '{sum+=$2; count++} END {if(count>0) printf "%.1f", sum/count; else print "0.0"}')
        omp_add_avg=$(grep "Add:" "$OUTPUT_DIR/stream_omp.log" | awk '{sum+=$2; count++} END {if(count>0) printf "%.1f", sum/count; else print "0.0"}')
        omp_triad_avg=$(grep "Triad:" "$OUTPUT_DIR/stream_omp.log" | awk '{sum+=$2; count++} END {if(count>0) printf "%.1f", sum/count; else print "0.0"}')

        omp_copy_max=$(grep "Copy:" "$OUTPUT_DIR/stream_omp.log" | awk '{if($2>max) max=$2} END {printf "%.1f", max+0}')
        omp_scale_max=$(grep "Scale:" "$OUTPUT_DIR/stream_omp.log" | awk '{if($2>max) max=$2} END {printf "%.1f", max+0}')
        omp_add_max=$(grep "Add:" "$OUTPUT_DIR/stream_omp.log" | awk '{if($2>max) max=$2} END {printf "%.1f", max+0}')
        omp_triad_max=$(grep "Triad:" "$OUTPUT_DIR/stream_omp.log" | awk '{if($2>max) max=$2} END {printf "%.1f", max+0}')
    fi

    # 保存结果到日志文件
    {
        echo "=== STREAM 测试结果摘要 ==="
        echo "单线程结果 (MB/s):"
        echo "  Copy:  平均=$single_copy_avg,  最大=$single_copy_max"
        echo "  Scale: 平均=$single_scale_avg, 最大=$single_scale_max"
        echo "  Add:   平均=$single_add_avg,   最大=$single_add_max"
        echo "  Triad: 平均=$single_triad_avg, 最大=$single_triad_max"
        
        if [ "$OPENMP_SUPPORT" = true ]; then
            echo "多线程结果 (MB/s):"
            echo "  Copy:  平均=$omp_copy_avg,  最大=$omp_copy_max"
            echo "  Scale: 平均=$omp_scale_avg, 最大=$omp_scale_max"
            echo "  Add:   平均=$omp_add_avg,   最大=$omp_add_max"
            echo "  Triad: 平均=$omp_triad_avg, 最大=$omp_triad_max"
        fi
    } > "$RESULT_LOG"

    # 生成MD格式报告
    generate_report "$cpu_model" "$cpu_cores" "$total_mem_gb" \
                   "$single_copy_avg" "$single_scale_avg" "$single_add_avg" "$single_triad_avg" \
                   "$single_copy_max" "$single_scale_max" "$single_add_max" "$single_triad_max" \
                   "$omp_copy_avg" "$omp_scale_avg" "$omp_add_avg" "$omp_triad_avg" \
                   "$omp_copy_max" "$omp_scale_max" "$omp_add_max" "$omp_triad_max"

    echo "[INFO] 结果分析完成" | tee -a "$LOG_FILE"
}

# 9. 报告生成函数
generate_report()
{
    local cpu_model="$1" cpu_cores="$2" total_mem_gb="$3"
    local s_copy_avg="$4" s_scale_avg="$5" s_add_avg="$6" s_triad_avg="$7"
    local s_copy_max="$8" s_scale_max="$9" s_add_max="${10}" s_triad_max="${11}"
    local o_copy_avg="${12}" o_scale_avg="${13}" o_add_avg="${14}" o_triad_avg="${15}"
    local o_copy_max="${16}" o_scale_max="${17}" o_add_max="${18}" o_triad_max="${19}"

    cat > "$REPORT_MD" << EOF
# STREAM 内存带宽测试报告

## 测试环境
- **测试时间**: $(date '+%Y-%m-%d %H:%M:%S')
- **测试平台**: $PLATFORM ($ARCH)
- **CPU型号**: $cpu_model
- **CPU核心数**: $cpu_cores
- **系统内存**: ${total_mem_gb}GB
- **测试轮数**: $para
- **OpenMP支持**: $([ "$OPENMP_SUPPORT" = true ] && echo "是" || echo "否")

## 测试结果

### 单线程性能 (MB/s)
| 操作类型 | 平均带宽 | 最大带宽 | 说明 |
|---------|---------|---------|------|
| Copy    | $s_copy_avg | $s_copy_max | 简单内存复制 (a[i] = b[i]) |
| Scale   | $s_scale_avg | $s_scale_max | 标量乘法 (a[i] = q*b[i]) |
| Add     | $s_add_avg | $s_add_max | 向量加法 (a[i] = b[i] + c[i]) |
| Triad   | $s_triad_avg | $s_triad_max | 组合操作 (a[i] = b[i] + q*c[i]) |

EOF

    if [ "$OPENMP_SUPPORT" = true ]; then
        cat >> "$REPORT_MD" << EOF
### 多线程性能 (MB/s, $cpu_cores 线程)
| 操作类型 | 平均带宽 | 最大带宽 | 扩展比 | 说明 |
|---------|---------|---------|-------|------|
| Copy    | $o_copy_avg | $o_copy_max | $(echo "scale=1; $o_copy_max / $s_copy_max" | bc 2>/dev/null || echo "N/A") | 简单内存复制 |
| Scale   | $o_scale_avg | $o_scale_max | $(echo "scale=1; $o_scale_max / $s_scale_max" | bc 2>/dev/null || echo "N/A") | 标量乘法 |
| Add     | $o_add_avg | $o_add_max | $(echo "scale=1; $o_add_max / $s_add_max" | bc 2>/dev/null || echo "N/A") | 向量加法 |
| Triad   | $o_triad_avg | $o_triad_max | $(echo "scale=1; $o_triad_max / $s_triad_max" | bc 2>/dev/null || echo "N/A") | 组合操作 |

EOF
    fi

    cat >> "$REPORT_MD" << EOF
## 性能分析

### 关键指标
- **Triad性能**: $([ "$OPENMP_SUPPORT" = true ] && echo "$o_triad_max" || echo "$s_triad_max") MB/s (最重要的综合指标)
- **内存带宽利用率**: 基于理论峰值带宽的百分比
- **多核扩展性**: $([ "$OPENMP_SUPPORT" = true ] && echo "$(echo "scale=1; $o_triad_max / $s_triad_max" | bc 2>/dev/null || echo "N/A")x" || echo "N/A (单线程)")

### 测试说明
STREAM基准测试通过四种不同的内存操作模式来评估系统的内存子系统性能：

1. **Copy**: 测试简单的内存到内存复制带宽
2. **Scale**: 测试内存读取、标量运算和内存写入的组合性能
3. **Add**: 测试双向量加法的内存带宽
4. **Triad**: 测试最复杂的内存操作，是最重要的性能指标

### 性能评估
- **优秀**: Triad > 50000 MB/s (高端服务器)
- **良好**: Triad > 20000 MB/s (主流服务器)
- **一般**: Triad > 10000 MB/s (入门级服务器)
- **较低**: Triad < 10000 MB/s (需要优化)

## 详细测试数据
- 单线程详细结果: stream_single.log
- $([ "$OPENMP_SUPPORT" = true ] && echo "多线程详细结果: stream_omp.log" || echo "多线程测试: 未执行 (OpenMP不支持)")
- 完整运行日志: stream_run.log
EOF

    echo "[INFO] 测试报告已生成: $REPORT_MD" | tee -a "$LOG_FILE"
}

# 10. 主流程
main()
{
    echo "[INFO] 开始STREAM内存带宽测试流程..." | tee -a "$LOG_FILE"

    # 执行安装
    if ! stream_install; then
        echo "[ERROR] STREAM安装失败" | tee -a "$LOG_FILE"
        exit 1
    fi

    # 执行测试
    if ! stream_test; then
        echo "[ERROR] STREAM测试失败" | tee -a "$LOG_FILE"
        exit 1
    fi

    # 显示完整的测试结果
    echo -e "\n============ STREAM 测试结果 ============"
    if [ -f "$REPORT_MD" ]; then
        cat "$REPORT_MD"
        echo -e "\n=========================================="
        echo "详细日志文件: $LOG_FILE"
        echo "单线程结果: $OUTPUT_DIR/stream_single.log"
        [ "$OPENMP_SUPPORT" = true ] && echo "多线程结果: $OUTPUT_DIR/stream_omp.log"
        echo "测试报告文件: $REPORT_MD"
    else
        echo "[WARNING] 测试报告文件未生成" | tee -a "$LOG_FILE"
    fi

    echo -e "\n[INFO] STREAM内存带宽测试流程完成" | tee -a "$LOG_FILE"
}

# 执行主流程
main