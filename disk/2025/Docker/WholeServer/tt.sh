#!/bin/bash
# 自动抓取SPEC config常用硬件参数

echo "================= 系统硬件信息采集 ================="

# CPU型号
cpu_model=$(lscpu | grep 'Model name' | head -1 | awk -F: '{print $2}' | sed 's/^ *//')
echo "hw_cpu_name = $cpu_model"

# CPU主频
cpu_mhz=$(lscpu | grep 'CPU MHz' | head -1 | awk -F: '{print $2}' | sed 's/^ *//')
echo "hw_cpu_nominal_mhz = $cpu_mhz"

# CPU最大主频
cpu_max_mhz=$(lscpu | grep 'CPU max MHz' | head -1 | awk -F: '{print $2}' | sed 's/^ *//')
echo "hw_cpu_max_mhz = $cpu_max_mhz"

# CPU核心数
cpu_cores=$(lscpu | grep '^CPU(s):' | head -1 | awk -F: '{print $2}' | sed 's/^ *//')
echo "hw_ncores = $cpu_cores"

# CPU颗数
cpu_sockets=$(lscpu | grep 'Socket(s):' | head -1 | awk -F: '{print $2}' | sed 's/^ *//')
echo "hw_nchips = $cpu_sockets"

# 每核线程数
threads_per_core=$(lscpu | grep 'Thread(s) per core' | head -1 | awk -F: '{print $2}' | sed 's/^ *//')
echo "hw_nthreadspercore = $threads_per_core"

# 内存总容量
mem_total=$(grep MemTotal /proc/meminfo | awk '{print $2/1024 " MB"}')
echo "hw_memory_total = $mem_total"

# 内存类型和条数（需要root权限）
if [ "$(id -u)" -ne 0 ]; then
    echo "hw_memory_type = (需要root权限采集)"
    echo "hw_memory_num = (需要root权限采集)"
else
    mem_info=$(dmidecode -t memory 2>/dev/null | grep -iE 'Part Number|Size' | grep -v 'No Module Installed')
    mem_type=$(echo "$mem_info" | grep 'Part Number' | awk '{print $3}' | sort | uniq | paste -sd "," -)
    mem_num=$(echo "$mem_info" | grep -c 'Size:')
    echo "hw_memory_type = $mem_type"
    echo "hw_memory_num = $mem_num"
fi

# L1/L2/L3缓存
l1d=$(lscpu | grep 'L1d cache' | awk -F: '{print $2}' | sed 's/^ *//')
l1i=$(lscpu | grep 'L1i cache' | awk -F: '{print $2}' | sed 's/^ *//')
l2=$(lscpu | grep 'L2 cache' | awk -F: '{print $2}' | sed 's/^ *//')
l3=$(lscpu | grep 'L3 cache' | awk -F: '{print $2}' | sed 's/^ *//')
echo "hw_pcache = L1d: $l1d, L1i: $l1i"
echo "hw_scache = L2: $l2"
echo "hw_tcache = L3: $l3"

# 操作系统
if [ -f /etc/os-release ]; then
    os_name=$(grep -E '^PRETTY_NAME=' /etc/os-release | cut -d '=' -f2- | tr -d '"')
elif [ -f /etc/redhat-release ]; then
    os_name=$(cat /etc/redhat-release)
else
    os_name=$(uname -a)
fi
echo "sw_os = $os_name"

# BIOS信息（需要root权限）
if [ "$(id -u)" -ne 0 ]; then
    echo "fw_bios = (需要root权限采集)"
else
    bios_info=$(dmidecode -t bios 2>/dev/null | grep -iE 'Vendor|Version|Release Date')
    echo "fw_bios = $(echo "$bios_info" | tr '\n' '; ')"
fi

echo "================= 采集完成 ================="

# 假设当前目录下已有csv数据，直接生成并运行python2绘图脚本

cat > plot_oltp.py << 'EOF'
# -*- coding: utf-8 -*-
import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt
import pandas as pd

workloads = {
    "oltp_read_only": u"只读 OLTP_READ_ONLY",
    "oltp_write_only": u"只写 OLTP_WRITE_ONLY",
    "oltp_read_write": u"读写 OLTP_READ_WRITE",
    "oltp_point_select": u"点查 OLTP_POINT_SELECT"
}

for wl in workloads:
    title = workloads[wl]
    try:
        df = pd.read_csv(wl + ".csv", names=["threads", "oltp"])
        df = df.sort_values("threads")
        plt.figure()
        plt.plot(df["threads"], df["oltp"], marker="o")
        plt.title(title)
        plt.xlabel("THREADS")
        plt.ylabel("OLTP")
        plt.grid(True)
        plt.savefig(wl + ".png")
        plt.close()
    except Exception as e:
        print("处理 %s 时出错: %s" % (wl, e))
EOF

python plot_oltp.py
rm -f plot_oltp.py 