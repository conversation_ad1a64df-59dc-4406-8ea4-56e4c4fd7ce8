#!/bin/sh
#==================================
#Author：胡泽志(<EMAIL>)
#0.5 release 板，实现基本功能，最终绘图
#输入参数(目标盘,以逗号分隔)：sdb,sdc,nvme0n1,nvme1n1......
#可以同时测试多片盘，也可以只测试一片盘
#useage:   muitiDrivePress1.0.sh     nvme0n1,sdb,nvme2n1
#V0.6  增加IO await的参数解析，考虑python增加绘图（pandas库CentOS8下安装有点问题，下一版再添加）
#V0.7 把 --allow_mounted_write=1 加上了
#V0.8 增加nvme list的log，同时考虑绘图。。。
#.        BW和IOPS里面也会把IO await的数据加入到csv，fix这个bug
#V0.9由于数据太多，图形太密集，将带宽顺序读和顺序写分别绘图，IOPS，RR和RW也分开；
#        IO await把RR、RW、SR、SW全部分开
#V1.0 在result界面输入每个nvme SSD的性能summary结果IOPS、BW和IO await（都是取最后100个值的平均值）
#V1.1 将顺序读写的64K更新为128K，因为一些新的Nand 在Page页上增大了，如果bs还是64K的话性能可能上不去；将预写从随机改成顺序，一个loop
#V1.2 增加写放大测试
#V1.3增加redhat8的兼容性
#V1.4修复数据采集的问题，5.x内核会在iosta里面显示nvme0c0n1;自动跳过OS盘（如果输错了）
#V1.5 修改了NVME测试的参数
#V1.6 修改了NVME测试的参数，增加--allow_mounted_write=1,同时修复多余10个nvme时数据采集问题
#V1.7 增加测试前后的smart-log存档，入口参数改成nvme或hdd或ssd（sata ssd），测试所有非OS的nvme ssd或hdd，变成离线版
#     生成markdown格式的报告
#V1.8 需要增加图床的功能，确保拷贝出去的markdown格式文档包含图片信息
#==================================

TestTime=7200 #default test 2 hours
PreConTime=1800 #pre_condition time 1200s random write
OUTPUT_DIR="${OUTPUT_DIR:-/home/<USER>/multiDrive_test}"
BASE_DIR="$OUTPUT_DIR"
echo "输出目录设置为: ${BASE_DIR}"
mkdir -p "$BASE_DIR" || {
        echo "错误: 无法创建日志目录 ${BASE_DIR}"
        exit 1
}
Target_Location="${BASE_DIR}/software"
Result_Location="${BASE_DIR}"
#DriveList=$1
Result=""

# 检查并修复yum功能（参考SpecJBB.sh方式）
fix_yum_and_install_deps() {
    echo "[INFO] 检查yum是否可用..."
    
    arch=$(uname -m)
    
    # 强制重置yum源
    echo "[INFO] 重置yum源..."
    rm -rf /etc/yum.repos.d/*
    
    if [[ "$arch" == "aarch64" ]]; then
        # ARM平台
        echo "[INFO] 检测到 ARM 架构，使用本地mtos-arm.repo"
        curl -o /etc/yum.repos.d/mtos-arm.repo http://************/huzz/DOCKER/tools/mtos-arm.repo
    else
        # x86平台
        echo "[INFO] 检测到 x86 架构，使用阿里云CentOS 7源"
        curl -o /etc/yum.repos.d/CentOS-Base.repo http://************/huzz/DOCKER/tools/Centos-7.repo
    fi
    
    # 清理并重建yum缓存
    yum clean all 2>/dev/null || true
    yum makecache fast 2>/dev/null || yum makecache 2>/dev/null || true
    
    # 安装必要的��赖工具
    echo "[INFO] 安装必要的依赖工具..."
    yum install -y sysstat fio util-linux-ng coreutils wget curl zip unzip bc gnuplot numactl lsscsi  smartmontools nvme-cli || {
        echo "[WARNING] 部分依赖安装失败，尝试继续执行"
    }
    
    # 验证关键命令是否可用
    if ! command -v iostat >/dev/null 2>&1; then
        echo "[ERROR] iostat命令不可用，请检查sysstat包安装"
        exit 1
    fi
    
    if ! command -v fio >/dev/null 2>&1; then
        echo "[ERROR] fio命令不可用，请检查fio包安装"
        exit 1
    fi
    
    echo "[INFO] 依赖工具安装完成"
}

show_help() {
     echo "Usage: $0 [-h|--help] <device_type> [test_duration]"
    echo ""
    echo "并行压测NVMe、HDD或SATA SSD性能的脚本工具"
    echo ""
    echo "必选参数:"
    echo "  device_type: nvme|hdd|ssd (不区分大小写)"
    echo "    nvme - 测试所有非系统盘的NVMe SSD"
    echo "    hdd  - 测试所有非系统盘的机械硬盘"
    echo "    ssd  - 测试所有非系统盘的SATA SSD"
    echo ""
    echo "可选参数:"
    echo "  test_duration: 测试时长(秒)，默认值7200"
    echo "    此参数将同时用作预处理时间和测试时间"
    echo ""
    echo "选项:"
    echo "  -h, --help    显示帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 nvme 3600  # 使用默认时间(7200秒)测试所有非系统盘的NVMe SSD"
    echo "  $0 hdd 3600   # 使用3600秒测试所有非系统盘的机械硬盘"
    echo "  $0 ssd 1800   # 使用1800秒测试所有非系统盘的SATA SSD"
    exit 0
}

# 添加 SMART 信息比对函数
compare_smart_logs() {
    local device=$1
    local before_log="${Result_Location}/smart_before_${device}.log"
    local after_log="${Result_Location}/smart_after_${device}.log"

    # 获取设备型号
    local model=""
    if [[ $device == nvme* ]]; then
        # 修改型号获取方式，确保只获取型号名称
        model=$(nvme id-ctrl /dev/$device | grep -i "mn" | sed 's/^.*: *//' | tr -d '\n' | sed 's/[[:space:]]*$//')
        local before_add_log="${Result_Location}/RW_WA_before_${device}.log"
        local after_add_log="${Result_Location}/RW_WA_after_${device}.log"
    else
        model=$(smartctl -i /dev/$device | grep "Device Model" | sed 's/^.*: *//' | tr -d '\n' | sed 's/[[:space:]]*$//')
    fi

    # 输出设备型号行，确保格式正确
    echo "| $device | $model | | | |"

    # 如果是 NVMe 设备
    if [[ $device == nvme* ]]; then
        # 定义要比对的属性列表
        local attrs=(
            "critical_warning"
            "temperature"
            "percentage_used"
            "power_cycles"
            "media_errors"
        )

        # 从 smart-log 中比对基本属性
        for attr in "${attrs[@]}"; do
            local before_val=$(grep -i "$attr" "$before_log" | awk '{print $NF}')
            local after_val=$(grep -i "$attr" "$after_log" | awk '{print $NF}')

            if [[ "$before_val" =~ ^[0-9]+$ ]] && [[ "$after_val" =~ ^[0-9]+$ ]]; then
                local change=$((after_val - before_val))
                echo "| $device | $attr | $before_val | $after_val | $change |"
            fi
        done

        # 从 smart-log-add 中比对额外属性
        if [ -f "$before_add_log" ] && [ -f "$after_add_log" ]; then
            local add_attrs=(
                "nand_bytes_written"
                "host_bytes_written"
                "program_fail_count"
                "erase_fail_count"
                "media_wear_percentage"
            )

            for attr in "${add_attrs[@]}"; do
                local before_val=$(grep -i "$attr" "$before_add_log" | awk '{print $NF}')
                local after_val=$(grep -i "$attr" "$after_add_log" | awk '{print $NF}')

                if [[ "$before_val" =~ ^[0-9]+$ ]] && [[ "$after_val" =~ ^[0-9]+$ ]]; then
                    local change=$((after_val - before_val))
                    echo "| $device | $attr | $before_val | $after_val | $change |"
                fi
            done
        fi
    else
        # 对于 HDD 和 SATA SSD
        model=$(smartctl -i /dev/$device | grep "Device Model" | sed 's/^.*: *//')
        echo "| $device | $model | | | |"

        # 定义要比对的属性列表
        local attrs=(
            "Raw_Read_Error_Rate"
            "Reallocated_Sector_Ct"
            "Power_On_Hours"
            "Power_Cycle_Count"
            "Temperature_Celsius"
        )

        for attr in "${attrs[@]}"; do
            local before_val=$(smartctl -A "$before_log" | grep "$attr" | awk '{print $10}')
            local after_val=$(smartctl -A "$after_log" | grep "$attr" | awk '{print $10}')

            if [[ "$before_val" =~ ^[0-9]+$ ]] && [[ "$after_val" =~ ^[0-9]+$ ]]; then
                local change=$((after_val - before_val))
                echo "| $device | $attr | $before_val | $after_val | $change |"
            fi
        done
    fi
}


generate_report() {
    local report_file="${Result_Location}/multiDrivePress_test.md"
    {
        echo "# 磁盘性能测试报告"

        echo "## 测试目的"
        echo "本测试脚本用于评估存储设备（NVMe SSD/SATA SSD/HDD）的性能指标，主要包括："
        echo "1. 随机读写性能（IOPS）"
        echo "   - 4K随机写（Random Write）：NVMe使用4线程/64队列深度，HDD使用16线程/16队列深度"
        echo "   - 4K随机读（Random Read）：NVMe使用8线程/256队列深度，HDD使用16线程/16队列深度"
        echo ""
        echo "2. 顺序读写性能（带宽）"
        echo "   - NVMe SSD: 128K块大小，1线程/256队列深度"
        echo "   - HDD/SATA SSD: 64K块大小，1线程/32队列深度"
        echo ""
        echo "3. IO延迟"
        echo "   - 读写操作的平均延迟（Average Latency）"
        echo "   - 读写操作的最大延迟（Maximum Latency）"
        echo ""
        echo "4. NVMe SSD特有指标"
        echo "   - 写放大系数（WAF：Write Amplification Factor）"
        echo "   - 设备温度监控"
        echo "   - SMART信息变化"
        echo ""
        echo "5. NUMA优化"
        echo "   - 自动检测NVMe设备所在NUMA节点"
        echo "   - FIO测试绑定到对应NUMA节点的CPU核心"
        echo "   - 避免跨NUMA节点访问，提高性能"
        echo ""
        echo "6. 测试特点"
        echo "   - 支持多设备并发测试"
        echo "   - 自动识别并排除系统盘"
        echo "   - 预处理：NVMe进行安全擦除后顺序写2轮，HDD随机写预处理30分钟"
        echo "   - 测试前后记录SMART信息用于设备健康状况对比"
        echo "   - 生成详细的性能曲线图"
        echo ""

        echo "## 测试时间: $(date '+%Y-%m-%d %H:%M:%S')"
        echo ""

        echo "## NUMA配置信息"
        echo "系统NUMA节点信息："
        lscpu | grep -E "^NUMA|^CPU\(s\)"
        echo ""
        echo "设备NUMA分布："
        for drive in $(echo $para | tr ',' ' '); do
            if [[ $drive == nvme* ]]; then
                numa_node=$(get_device_numa $drive)
                cpu_list=$(get_numa_cpus $numa_node)
                echo "$drive -> NUMA节点: $numa_node"
                echo "    使用CPU核心: $cpu_list"
            fi
        done
        echo ""

        echo "## 被测试设备"
        echo "将要测试的设备: $para"

        echo "## 测试结果"
        echo "### 性能结果"

        echo "$Result" | sed 's/\\n/\n/g' | grep -v '^\[.*\]'

        echo ""

        echo "### 延时测试结果"
        echo "#### 平均延时（Average Latency）"

        echo "$Result_await" | sed 's/\\n/\n/g' | grep -v '^\[.*\]'

        echo ""

        echo "#### 最大延时（Maximum Latency）"

        echo "$Result_await_Max" | sed 's/\\n/\n/g' | grep -v '^\[.*\]'

        echo ""

        echo "### SMART信息变化对比"
        echo "| 目标盘 | 指标 | 测试前 | 测试后 | 变化值 |"
        echo "|---------|---------|---------|---------|---------|"
        for drive in $(echo $para | tr ',' ' '); do
            compare_smart_logs "$drive"
        done
        echo ""
        echo "完整SMART日志见附件：smart_before_*.log 和 smart_after_*.log"
        echo ""

        echo "### 性能图表"
        echo "#### 1. IOPS性能"
        echo "##### 随机读IOPS"
        echo "![随机读IOPS](${Result_Location}/Summary_IOPS_RR.png)"

        echo "##### 随机写IOPS"
        echo "![随机写IOPS](${Result_Location}/Summary_IOPS_RW.png)"

        echo "#### 2. 带宽性能"
        echo "##### 顺序读带宽"
        echo "![顺序读带宽](${Result_Location}/Summary_BW_SR.png)"
        echo "##### 顺序写带宽"
        echo "![顺序写带宽](${Result_Location}/Summary_BW_SW.png)"

        echo "#### 3. IO延迟"
        echo "##### 随机读延迟"
        echo "![随机读延迟](${Result_Location}/Summary_IOawait_RR.png)"
        echo "##### 随机写延迟"
        echo "![随机写延迟](${Result_Location}/Summary_IOawait_RW.png)"
        echo "##### 顺序读延迟"
        echo "![顺序读延迟](${Result_Location}/Summary_IOawait_SR.png)"
        echo "##### 顺序写延迟"
        echo "![顺序写延迟](${Result_Location}/Summary_IOawait_SW.png)"
        echo ""

        if [[ $elem == nvme* ]]; then
            echo "#### 4. NVMe温度监控"
            echo "![温度监控](${Result_Location}/Summary_temp.png)"
        fi

    } > "$report_file"

    echo "性能测试报告已生成：${report_file}"
}


# 获取 NVMe 设备的 NUMA 节点
get_device_numa() {
    local device=$1
    local numa_node

    if [[ $device == nvme* ]]; then
        # 从设备名称中提取 nvme 编号（例如：从 nvme0n1 提取 0）
        local nvme_num=${device//[^0-9]/}
        nvme_num=${nvme_num:0:1}

        # 获取 NUMA 节点
        if [ -f "/sys/class/nvme/nvme${nvme_num}/device/numa_node" ]; then
            numa_node=$(cat "/sys/class/nvme/nvme${nvme_num}/device/numa_node")
            # 如果值为负数，设为0
            if [ "$numa_node" -lt 0 ]; then
                numa_node=0
            fi
        else
            # 如果无法获取 NUMA 信息，默认使用节点0
            numa_node=0
        fi
    else
        # 非 NVMe 设备直接返回 0
        numa_node=0
    fi

    echo $numa_node
}

# 获取 NUMA 节点的 CPU 列表
get_numa_cpus() {
    local numa_node=$1
    local cpu_list

    cpu_list=$(lscpu -p=CPU,NODE | grep "^[0-9]*,${numa_node}$" | cut -d',' -f1 | tr '\n' ',')
    cpu_list=${cpu_list%,}  # 移除最后的逗号

    echo $cpu_list
}
get_system_disk() {
    # 首先尝试从根分区获取
    local root_part=$(df / | awk 'NR==2 {print $1}')
    local disk_name=""

    # 检查是否是标准设备路径
    if [[ "$root_part" =~ ^/dev/(sd|nvme|vd) ]]; then
        # 提取设备名称（移除分区号）
        disk_name=$(echo "$root_part" | sed -E 's|/dev/||; s/[0-9]+$//')
    else
        # 如果根分区不是标准设备，尝试从/boot获取
        root_part=$(df /boot 2>/dev/null | awk 'NR==2 {print $1}')
        if [[ "$root_part" =~ ^/dev/(sd|nvme|vd) ]]; then
            disk_name=$(echo "$root_part" | sed -E 's|/dev/||; s/[0-9]+$//')
        else
            # 如果仍然找不到，返回一个安全的默认值
            echo "sda"
            return
        fi
    fi

    echo "$disk_name"
}
get_target_drives() {
    local type=$1
    local os_disk=$(get_system_disk)
    local drive_list=""

    echo "调试: 系统盘为 $os_disk" >&2

    type=$(echo $type | tr '[:upper:]' '[:lower:]')
    case $type in
        "nvme")
            # 修复这里：过滤掉无效的设备名
            drive_list=$(nvme list | awk 'NR>2 && /^\/dev\/nvme/ {print $1}' | cut -d'/' -f3 | grep -v "^$os_disk" | grep -E '^nvme[0-9]+n[0-9]+$' | tr '\n' ',' | sed 's/,$//')
            ;;
        "hdd")
            drive_list=$(lsblk -d -o name,rota,type | awk '$2=="1" && $3=="disk" {print $1}' | grep -v "$os_disk" | tr '\n' ',' | sed 's/,$//')
            ;;
        "ssd")
            drive_list=$(lsblk -d -o name,rota,type | awk '$2=="0" && $3=="disk" && $1!~/^nvme/ {print $1}' | grep -v "$os_disk" | tr '\n' ',' | sed 's/,$//')
            ;;
        *)
            echo "错误: 无效的设备类型 '$type'" >&2
            return 1
            ;;
    esac

    # 添加调试信息
    if [ -z "$drive_list" ]; then
        echo "警告: 未检测到符合条件的 $type 设备" >&2
    else
        echo "调试: 检测到的设备列表: $drive_list" >&2
    fi

    echo "$drive_list"
}


# 添加 fio 测试函数
run_fio_test() {
    local device=$1
    local test_type=$2
    local output_file=$3
    local numa_node=0
    local cpu_list=""

    # 只对 NVMe 设备进行 NUMA 绑定
    if [[ $device == nvme* ]]; then
        numa_node=$(get_device_numa $device)
        cpu_list=$(get_numa_cpus $numa_node)
        echo "Device $device is bound to NUMA node $numa_node"
    fi

    case $test_type in
        "precondition")
            if [[ $device == nvme* ]]; then
                numactl --cpunodebind=$numa_node --membind=$numa_node \
                fio --ioengine=libaio --randrepeat=0 --norandommap --thread \
                    --direct=1 --allow_mounted_write=1 --group_reporting \
                    --name=PreCon_RW --loops=2 --numjobs=1 --iodepth=128 \
                    --filename=/dev/${device} --rw=randwrite --bs=8k \
                    --output=${output_file} \
                    --cpus_allowed=$cpu_list --cpus_allowed_policy=split
            else
                fio --ioengine=libaio --randrepeat=0 --norandommap --thread \
                    --direct=1 --group_reporting --name=HDD_PreCon \
                    --ramp_time=10 --runtime=${PreConTime} --time_based \
                    --numjobs=16 --iodepth=16 --filename=/dev/${device} \
                    --rw=randwrite --bs=8k --output=${output_file}
            fi
            ;;

        "rw")
            if [[ $device == nvme* ]]; then
                numactl --cpunodebind=$numa_node --membind=$numa_node \
                fio --ioengine=libaio --randrepeat=0 --norandommap --thread \
                    --direct=1 --allow_mounted_write=1 --group_reporting \
                    --name=NVMe_RW --ramp_time=3 --runtime=${TestTime} \
                    --time_based --numjobs=4 --iodepth=64 \
                    --filename=/dev/${device} --rw=randwrite --bs=4k \
                    --output=${output_file} \
                    --cpus_allowed=$cpu_list --cpus_allowed_policy=split
            else
                fio --ioengine=libaio --randrepeat=0 --norandommap --thread \
                    --direct=1 --group_reporting --name=HDD_RW \
                    --ramp_time=3 --runtime=${TestTime} --time_based \
                    --numjobs=16 --iodepth=16 --filename=/dev/${device} \
                    --rw=randwrite --bs=4k --output=${output_file}
            fi
            ;;

        "rr")
            if [[ $device == nvme* ]]; then
                numactl --cpunodebind=$numa_node --membind=$numa_node \
                fio --ioengine=libaio --randrepeat=0 --norandommap --thread \
                    --direct=1 --allow_mounted_write=1 --group_reporting \
                    --name=NVMe_RR --ramp_time=3 --runtime=${TestTime} \
                    --time_based --numjobs=8 --iodepth=256 \
                    --filename=/dev/${device} --rw=randread --bs=4k \
                    --output=${output_file} \
                    --cpus_allowed=$cpu_list --cpus_allowed_policy=split
            else
                fio --ioengine=libaio --randrepeat=0 --norandommap --thread \
                    --direct=1 --group_reporting --name=HDD_RR \
                    --ramp_time=3 --runtime=${TestTime} --time_based \
                    --numjobs=16 --iodepth=16 --filename=/dev/${device} \
                    --rw=randread --bs=4k --output=${output_file}
            fi
            ;;

        "sw")
            if [[ $device == nvme* ]]; then
                numactl --cpunodebind=$numa_node --membind=$numa_node \
                fio --ioengine=libaio --randrepeat=0 --norandommap --thread \
                    --direct=1 --allow_mounted_write=1 --group_reporting \
                    --name=NVMe_SW --ramp_time=3 --runtime=${TestTime} \
                    --time_based --numjobs=1 --iodepth=256 \
                    --filename=/dev/${device} --rw=write --bs=128k \
                    --output=${output_file} \
                    --cpus_allowed=$cpu_list --cpus_allowed_policy=split
            else
                fio --ioengine=libaio --randrepeat=0 --norandommap --thread \
                    --direct=1 --group_reporting --name=HDD_SW \
                    --ramp_time=3 --runtime=${TestTime} --time_based \
                    --numjobs=1 --iodepth=32 --filename=/dev/${device} \
                    --rw=write --bs=64k --output=${output_file}
            fi
            ;;

        "sr")
            if [[ $device == nvme* ]]; then
                numactl --cpunodebind=$numa_node --membind=$numa_node \
                fio --ioengine=libaio --randrepeat=0 --norandommap --thread \
                    --direct=1 --allow_mounted_write=1 --group_reporting \
                    --name=NVMe_SR --ramp_time=3 --runtime=${TestTime} \
                    --time_based --numjobs=1 --iodepth=256 \
                    --filename=/dev/${device} --rw=read --bs=128k \
                    --output=${output_file} \
                    --cpus_allowed=$cpu_list --cpus_allowed_policy=split
            else
                fio --ioengine=libaio --randrepeat=0 --norandommap --thread \
                    --direct=1 --group_reporting --name=HDD_SR \
                    --ramp_time=3 --runtime=${TestTime} --time_based \
                    --numjobs=1 --iodepth=32 --filename=/dev/${device} \
                    --rw=read --bs=64k --output=${output_file}
            fi
            ;;

        *)
            echo "错误: 未知的测试类型 '$test_type'"
            return 1
            ;;
    esac
}


Init()
{
    # 删除历史数据
    rm -rf "$Target_Location"
    if mountpoint -q "$Result_Location"; then
        # 如果是挂载点，只删除内容
        rm -rf "${Result_Location:?}"/*
    else
        # 如果不是挂载点，可以整个删除重建
        rm -rf "$Result_Location"
    fi

    mkdir -p "$Target_Location"
    mkdir -p "$Result_Location"

    fix_yum_and_install_deps

    # 检查必要的工具并自动安装
    for tool in fio gnuplot numactl sysstat lsscsi; do
        if ! type $tool >/dev/null 2>&1; then
            echo "[WARN] 未检测到 $tool，尝试自动安装..."
            yum install -y $tool || echo "[ERROR] $tool 安装失败，请手动检查！"
        fi
    done
}

get_centos_version() {
    echo $(cat /etc/os-release | grep ^VERSION_ID= | cut -d\" -f 2)
}


##绘图函数
gen_gnuplot_input()
 {
    ## $1 csv文件名 y坐标标题  $2
    str=( `head -n +1 $1` )
    str=${str//,/ }
    arr=($str)
    i=1
    cmd="plot"
    for ele in ${arr[@]}
    do
        #echo "$ele"
        #提出一列
        #cat $1 |awk '{print $x}' x=$i |tail -n +2 > tmp_$ele.log
        #tit=`nvme list |grep $ele |awk '{print $3,$4}'`
        cstr="'$1' using 0:$i with lines title '$ele',"
        cmd=${cmd}${cstr}
        let i=$i+1
    done
#    echo $cmd
    #DriveList=`nvme list |awk 'NR>2' |awk '{print $1,$3,$4}'`
    tt=$1
    ylable=$(echo ${tt%.*})
    Path=${tt%/*}
    colors=(a7001f 0a60c2 b2582b 21a66c d6604d 4393c3 f4a582 92c5de edcbb7 b1c5d0)
    ## 设置 gnulot 绘图的一些属性，包括x 轴，y轴等信息，输出文件保存为png， 曲线的颜色等等
    echo -n "
    set key box;
    set datafile separator \",\";
    set terminal png size 1440,900 font \",12\";
    set xlabel \"Cycle\";
    set ylabel '$ylable';
    set output \"$ylable.png\";
    set samples 50;
    #set label '$2'at 0.5,590;
    set style data lines;"
    echo -n $cmd
   ## 将第一次终端传入参数，即要分析的日志文件名，传到函数中，处理后，用gnullot画图
   #用法：gen_gnuplot_input $1 |gnuplot -persist
}


main()
{

    # 处理命令行参数
    if [ "$1" = "-h" ] || [ "$1" = "--help" ]; then
        show_help
        exit 0
    fi

    if [ $# -eq 0 ] || [ $# -gt 2 ]; then
        echo "错误: 参数数量不正确"
        show_help
        exit 1
    fi

    # 检查参数是否为有效值
    case $(echo "$1" | tr '[:upper:]' '[:lower:]') in
        "nvme"|"hdd"|"ssd") ;;
        *)
            echo "错误: 无效的设备类型 '$1'"
            show_help
            exit 1
            ;;
    esac

    # 处理可选的测试时间参数
    if [ -n "$2" ]; then
        if [[ "$2" =~ ^[0-9]+$ ]]; then
            TestTime=$2
            PreConTime=$2
            echo "使用指定的测试时间: $TestTime 秒"
        else
            echo "错误: 测试时间必须是正整数"
            show_help
            exit 1
        fi
    else
        echo "使用默认测试时间: $TestTime 秒"
    fi

    Init;
    # 获取目标设备列表
    para=$(get_target_drives $1)
    if [ -z "$para" ]; then
        echo "未找到符合条件的设备"
        exit 1
    fi

    echo "将要测试的设备: $para"

    # 4. 在测试开始前收集smart信息
    for drive in $(echo $para | tr ',' ' '); do
        if [[ $drive == nvme* ]]; then
            nvme smart-log /dev/$drive > ${Result_Location}/smart_before_${drive}.log
        else
            smartctl -a /dev/$drive > ${Result_Location}/smart_before_${drive}.log
        fi
    done


    iostat -xm 1 1 |grep Device | sed 's/[ ][ ]*/\n/g ' > location.log
    num_RR=$(sed -n  '/r\/s/=' location.log)
    num_RW=$(sed -n  '/w\/s/=' location.log)
    num_SR=$(sed -n  '/rMB\/s/=' location.log)
    num_SW=$(sed -n  '/wMB\/s/=' location.log)
    num_Rawait=$(sed -n  '/r_await/=' location.log)
    num_Wawait=$(sed -n  '/w_await/=' location.log)
    rm -f location.log

    centos_ver=$(get_centos_version)
    if [[ "${centos_ver}" = "7" ]]
    then
        WA=5
    elif [[ "${centos_ver}" = "8" ]]
    then
        WA=4
    elif [[ "${centos_ver}" = "22.03" ]]
    then
        WA=4
    else
        WA=5
    fi

    OSDisk=`df -Th | grep "/$" |awk '{print $1}'|awk -F"/" '{print $3}' |tr -d 0-9`

    nvme list > ${Result_Location}/nvmelist.log
    str=$para #$DriveList
    #str=$DriveList
    arr=(`echo $str | tr ',' ' '`)

    #如果测试目标盘有NVMe盘,先对整盘顺序写2个loop
    #=======================================================
    i=0
    for elem in ${arr[@]}
    do
        if [[ $elem == nvme* ]];
        then
            echo "secure erase $elem first, then 2 loops RW for $elem"
            nvme format /dev/$elem -ses=1;
            run_fio_test $elem "precondition" "${Result_Location}/PreCon_RW_${elem}.out" &
            fio --ioengine=libaio --randrepeat=0 --norandommap --thread --direct=1 --allow_mounted_write=1 --group_reporting --name=PreCon_RW  --loops=1 --numjobs=1 --iodepth=128 --filename=/dev/${elem} --rw=randwrite --bs=8k --output=${Result_Location}/PreCon_RW_${elem}.out &
            echo "$elem temprature" > ${Result_Location}/tmp_WholeTest_$elem.log
            while :; do nvme smart-log /dev/$elem |grep temp >> ${Result_Location}/tmp_WholeTest_$elem.log; sleep 1;done &
            pid=`echo $!`
	        pid_arr[$i]=$pid
	   else
	        if [[ $elem = ${OSDisk} ]];
	        then
	            echo "$elem is OS drive, please Skip this one...."
	        else
	            echo "The target Drive is SATA HDD/SSD: $elem"
                run_fio_test $elem "precondition" "${Result_Location}/HDD_PreCon_${elem}.out" &
	            #fio --ioengine=libaio --randrepeat=0 --norandommap --thread --direct=1 --group_reporting --name=HDD_PreCon --ramp_time=10 --runtime=${PreConTime} --time_based --numjobs=16 --iodepth=16 --filename=/dev/${elem} --rw=randwrite --bs=8k --output=${Result_Location}/HDD_PreCon_${elem}.out &
	        fi
       fi
       let i=$i+1
    done
    sleep $PreConTime;  #实际只做30分钟的预写
    pkill fio
    #=======================================================
    #RW
    echo "" > ${Result_Location}/IOPS_RW.log
    iostat -mx 1 -t $TestTime  >> ${Result_Location}/IOPS_RW.log &
    for elem in ${arr[@]}
    do
        if [[ $elem == sd* ]];
        then
            if [[ $elem = ${OSDisk} ]];
	        then
	            echo "$elem is OS drive, please Skip this one...."
	        else
	            echo "HDD IOPS_RW test $elem"
                run_fio_test $elem "rw" "${Result_Location}/HDD_RW_${elem}.out" &
                #fio --ioengine=libaio --randrepeat=0 --norandommap --thread --direct=1 --group_reporting --name=HDD_RW --ramp_time=3 --runtime=${TestTime} --time_based --numjobs=16 --iodepth=16 --filename=/dev/${elem} --rw=randwrite --bs=4k --output=${Result_Location}/HDD_RW_${elem}.out &
            fi
        else
            nvme intel smart-log-add /dev/${elem} > ${Result_Location}/RW_WA_before_${elem}.log
            echo "NVMe IOPS_RW test $elem"
            run_fio_test $elem "rw" "${Result_Location}/NVMe_RW_${elem}.out" &
            #fio --ioengine=libaio --randrepeat=0 --norandommap --thread --direct=1 --allow_mounted_write=1 --group_reporting --name=NVMe_RW --ramp_time=3 --runtime=${TestTime} --time_based --numjobs=4 --iodepth=64 --filename=/dev/${elem} --rw=randwrite --bs=4k --output=${Result_Location}/NVMe_RW_${elem}.out &
       fi
    done
    sleep $TestTime;
    for elem in ${arr[@]}
    do
        nvme intel smart-log-add /dev/${elem} > ${Result_Location}/RW_WA_after_${elem}.log
    done
    sleep 10s; #加10s
    #RR
     echo "" > ${Result_Location}/IOPS_RR.log
    iostat -mx 1 -t $TestTime >> ${Result_Location}/IOPS_RR.log &
    for elem in ${arr[@]}
    do
        if [[ $elem == sd* ]];
        then
             if [[ $elem = ${OSDisk} ]];
	        then
	            echo "$elem is OS drive, please Skip this one...."
	        else
	            echo "HDD IOPS_RR test $elem"
                run_fio_test $elem "rr" "${Result_Location}/HDD_RR_${elem}.out" &
                #fio --ioengine=libaio --randrepeat=0 --norandommap --thread --direct=1 --group_reporting --name=HDD_RR --ramp_time=3 --runtime=${TestTime} --time_based --numjobs=16 --iodepth=16 --filename=/dev/${elem} --rw=randread --bs=4k --output=${Result_Location}/HDD_RR_${elem}.out &
            fi
        else
            echo "NVMe IOPS_RR test $elem"
            run_fio_test $elem "rr" "${Result_Location}/NVMe_RR_${elem}.out" &
            #fio --ioengine=libaio --randrepeat=0 --norandommap --thread --direct=1 --allow_mounted_write=1 --group_reporting --name=NVMe_RR --ramp_time=3 --runtime=${TestTime} --time_based --numjobs=8 --iodepth=256 --filename=/dev/${elem} --rw=randread --bs=4k --output=${Result_Location}/NVMe_RR_${elem}.out &
       fi
    done
    sleep $TestTime;
    sleep 10s; #加10s
    #SW
    echo "" > ${Result_Location}/BW_SW.log
    iostat -mx 1 -t $TestTime  >> ${Result_Location}/BW_SW.log &
    for elem in ${arr[@]}
    do
        if [[ $elem == sd* ]];
        then
             if [[ $elem = ${OSDisk} ]];
	        then
	            echo "$elem is OS drive, please Skip this one...."
	        else
	            echo "HDD BW_SW test $elem"
                run_fio_test $elem "sw" "${Result_Location}/HDD_SW_${elem}.out" &
                #fio --ioengine=libaio --randrepeat=0 --norandommap --thread --direct=1 --group_reporting --name=HDD_SW --ramp_time=3 --runtime=${TestTime} --time_based --numjobs=1 --iodepth=32 --filename=/dev/${elem} --rw=write --bs=64k --output=${Result_Location}/HDD_SW_${elem}.out &
            fi
        else
            nvme intel smart-log-add /dev/${elem} > ${Result_Location}/SW_WA_before_${elem}.log
            echo "NVMe BW_SW test $elem"  # 修改这里的提示信息
            run_fio_test $elem "sw" "${Result_Location}/NVMe_SW_${elem}.out" &
            #fio --ioengine=libaio --randrepeat=0 --norandommap --thread --direct=1 --allow_mounted_write=1 --group_reporting --name=NVMe_SW --ramp_time=3 --runtime=${TestTime} --time_based --numjobs=1 --iodepth=256 --filename=/dev/${elem} --rw=write --bs=128k --output=${Result_Location}/NVMe_SW_${elem}.out &
       fi
    done
    sleep $TestTime
    for elem in ${arr[@]}
    do
        nvme intel smart-log-add /dev/${elem} > ${Result_Location}/SW_WA_after_${elem}.log
    done
    sleep 10s #加10s
    #SR
    echo "" > ${Result_Location}/BW_SR.log
    iostat -mx 1 -t $TestTime  >> ${Result_Location}/BW_SR.log &
    for elem in ${arr[@]}
    do
        if [[ $elem == sd* ]];
        then
             if [[ $elem = ${OSDisk} ]];
	        then
	            echo "$elem is OS drive, please Skip this one...."
	        else
	            echo "HDD BW_SR test $elem"
                run_fio_test $elem "sr" "${Result_Location}/HDD_SR_${elem}.out" &
                #fio --ioengine=libaio --randrepeat=0 --norandommap --thread --direct=1 --group_reporting --name=HDD_SR --ramp_time=3 --runtime=${TestTime} --time_based --numjobs=1 --iodepth=32 --filename=/dev/${elem} --rw=read --bs=64k --output=${Result_Location}/HDD_SR_${elem}.out &
            fi
        else
            echo "NVMe BW_SR test $elem"  # 修改这里的提示信息
            run_fio_test $elem "sr" "${Result_Location}/NVMe_SR_${elem}.out" &
            #fio --ioengine=libaio --randrepeat=0 --norandommap --thread --direct=1 --allow_mounted_write=1 --group_reporting --name=NVMe_SR --ramp_time=3 --runtime=${TestTime} --time_based --numjobs=1 --iodepth=256 --filename=/dev/${elem} --rw=read --bs=128k --output=${Result_Location}/NVMe_SR_${elem}.out &
       fi
    done
    sleep $TestTime
    sleep 10s #加10s

    #结束温度监测进程
    i=0
    for elem in ${arr[@]}
    do
        if [[ $elem == nvme* ]];
        then
            echo "Kill $elem temprature recode process"
            pid=${pid_arr[$i]}
	    let i=$i+1
	    kill -9 $pid
	    echo "$elem" > ${Result_Location}/tmp_WholeTest_$elem.csv
	    cat ${Result_Location}/tmp_WholeTest_$elem.log |awk '{print $3}' >> ${Result_Location}/tmp_WholeTest_$elem.csv
       fi
    done

    if [[ $elem == sd* ]];
    then
        lsscsi > hddlist.log
        Result=`cat hddlist.log`
        Result=$Result"\n"
        rm -f  hddlist.log
    else #nvme
        nvme list > nvmelist.log
        Result=$(cat nvmelist.log)
        Result=$Result"\n"
        rm -f nvmelist.log
    fi

    #data process
    echo "Data Process...."
    for elem1 in ${arr[@]}
    do
        elem=$elem1
	    echo "process $elem"
        # RW
            echo "RW_$elem" > ${Result_Location}/IOPS_RW_$elem.csv
            echo "RW_$elem" > ${Result_Location}/IOPS_RWawait_$elem.csv
        # cat ${Result_Location}/IOPS_RW.log |grep $elem > ${Result_Location}/IOPS_RW_$elem.log
            cat ${Result_Location}/IOPS_RW.log | grep -E "(^|[[:space:]])${elem1}([[:space:]]|$)" > ${Result_Location}/IOPS_RW_$elem.log
            cat ${Result_Location}/IOPS_RW_$elem.log | awk '{print $x}' x="${num_RW}" >> ${Result_Location}/IOPS_RW_$elem.csv
            sed -i '$d' ${Result_Location}/IOPS_RW_$elem.csv  #删除最后一行
            sed -i '$d' ${Result_Location}/IOPS_RW_$elem.csv  #删除最后一行
            sed -i '$d' ${Result_Location}/IOPS_RW_$elem.csv  #删除最后一行
            tmp=$(cat ${Result_Location}/IOPS_RW_$elem.csv |awk -F "," '{print $1}' |tail -n 100 |awk '{sum+=$1} END {print sum/NR}') #取最后100行的平均值
            Result=$Result"${elem}_RW:${tmp} "

            if [[ $elem == nvme* ]];
            then
                #写放大计算
                WA1_nand=$(awk '/nand_bytes_written/ {print $NF}' ${Result_Location}/RW_WA_before_${elem}.log | head -1)
                WA2_nand=$(awk '/nand_bytes_written/ {print $NF}' ${Result_Location}/RW_WA_after_${elem}.log | head -1)
                WA1_host=$(awk '/host_bytes_written/ {print $NF}' ${Result_Location}/RW_WA_before_${elem}.log | head -1)
                WA2_host=$(awk '/host_bytes_written/ {print $NF}' ${Result_Location}/RW_WA_after_${elem}.log | head -1)
                #WA1_nand=`cat ${Result_Location}/RW_WA_before_${elem}n1.log |grep nand_bytes_written |awk '{print $x}' x="${WA}"`
                #WA2_nand=`cat ${Result_Location}/RW_WA_after_${elem}n1.log |grep nand_bytes_written |awk '{print $x}' x="${WA}"`
                #WA1_host=`cat ${Result_Location}/RW_WA_before_${elem}n1.log |grep host_bytes_written |awk '{print $x}' x="${WA}"`
                #WA2_host=`cat ${Result_Location}/RW_WA_after_${elem}n1.log |grep host_bytes_written |awk '{print $x}' x="${WA}"`
                t1=$((WA2_nand-WA1_nand))
                echo "t1=$t1"
                t2=$((WA2_host-WA1_host))
                echo "t2=$t2"
                RW_WA=$(echo "scale=4; $t1/$t2" | bc)
                echo "the RW_WA result is:$RW_WA"
            fi

            #增加IO await数据解析
            cat ${Result_Location}/IOPS_RW_$elem.log | awk '{print $x}' x="${num_Wawait}" >> ${Result_Location}/IOPS_RWawait_$elem.csv
            sed -i '$d' ${Result_Location}/IOPS_RWawait_$elem.csv  #删除最后一行
            sed -i '$d' ${Result_Location}/IOPS_RWawait_$elem.csv  #删除最后一行
            sed -i '$d' ${Result_Location}/IOPS_RWawait_$elem.csv  #删除最后一行
            tmp=$(cat ${Result_Location}/IOPS_RWawait_$elem.csv |awk -F "," '{print $1}' |tail -n 100 |awk '{sum+=$1} END {print sum/NR}') #取最后100行的平均值
            tmp_max=$(cat ${Result_Location}/IOPS_RWawait_$elem.csv |awk -F , '{print $1}' |sort -r |sed -n "2,1p")
            Result_await=$Result_await"${elem}_RW_await:$tmp "
            Result_await_Max=$Result_await_Max"${elem}_RW_await_Max:$tmp_max "
        # RR
            echo "RR_$elem" > ${Result_Location}/IOPS_RR_$elem.csv
            echo "RR_$elem" > ${Result_Location}/IOPS_RRawait_$elem.csv
        # cat ${Result_Location}/IOPS_RR.log |grep $elem > ${Result_Location}/IOPS_RR_$elem.log
            cat ${Result_Location}/IOPS_RR.log | grep -E "(^|[[:space:]])${elem1}([[:space:]]|$)" > ${Result_Location}/IOPS_RR_$elem.log
            cat ${Result_Location}/IOPS_RR_$elem.log | awk '{print $x}' x="${num_RR}" >> ${Result_Location}/IOPS_RR_$elem.csv
            sed -i '$d' ${Result_Location}/IOPS_RR_$elem.csv  #删除最后一行
            sed -i '$d' ${Result_Location}/IOPS_RR_$elem.csv  #删除最后一行
            sed -i '$d' ${Result_Location}/IOPS_RR_$elem.csv  #删除最后一行
            tmp=$(cat ${Result_Location}/IOPS_RR_$elem.csv |awk -F "," '{print $1}' |tail -n 100 |awk '{sum+=$1} END {print sum/NR}') #取最后100行的平均值
            ttt=$(echo $tmp |awk '{printf("%d",$0)}')
            Result=$Result" RR:$ttt "
             #增加IO await数据解析
            cat ${Result_Location}/IOPS_RR_$elem.log | awk '{print $x}' x="${num_Rawait}" >> ${Result_Location}/IOPS_RRawait_$elem.csv
            sed -i '$d' ${Result_Location}/IOPS_RRawait_$elem.csv  #删除最后一行
            sed -i '$d' ${Result_Location}/IOPS_RRawait_$elem.csv  #删除最后一行
            sed -i '$d' ${Result_Location}/IOPS_RRawait_$elem.csv  #删除最后一行
            tmp=$(cat ${Result_Location}/IOPS_RRawait_$elem.csv |awk -F "," '{print $1}' |tail -n 100 |awk '{sum+=$1} END {print sum/NR}') #取最后100行的平均值
            tmp_max=$(cat ${Result_Location}/IOPS_RRawait_$elem.csv |awk -F , '{print $1}' |sort -r |sed -n "2,1p")
            Result_await=$Result_await" RR_await:$tmp "
            Result_await_Max=$Result_await_Max" RR_await_Max:$tmp_max "
        # SW
            echo "SW_$elem" > ${Result_Location}/BW_SW_$elem.csv
            echo "SW_$elem" > ${Result_Location}/BW_SWawait_$elem.csv
        # cat ${Result_Location}/BW_SW.log |grep $elem > ${Result_Location}/BW_SW_$elem.log
            cat ${Result_Location}/BW_SW.log | grep -E "(^|[[:space:]])${elem1}([[:space:]]|$)" > ${Result_Location}/BW_SW_$elem.log
            cat ${Result_Location}/BW_SW_$elem.log | awk '{print $x}' x="${num_SW}" >> ${Result_Location}/BW_SW_$elem.csv
            sed -i '$d' ${Result_Location}/BW_SW_$elem.csv  #删除最后一行
            sed -i '$d' ${Result_Location}/BW_SW_$elem.csv  #删除最后一行
            sed -i '$d' ${Result_Location}/BW_SW_$elem.csv  #删除最后一行
            tmp=$(cat ${Result_Location}/BW_SW_$elem.csv |awk -F "," '{print $1}' |tail -n 100 |awk '{sum+=$1} END {print sum/NR}') #取最后100行的平均值
            Result=$Result" SW:${tmp} "

            #写放大计算
            if [[ $elem == nvme* ]];
            then
                WA1_nand=$(awk '/nand_bytes_written/ {print $NF}' ${Result_Location}/SW_WA_before_${elem}.log | head -1)
                WA2_nand=$(awk '/nand_bytes_written/ {print $NF}' ${Result_Location}/SW_WA_after_${elem}.log | head -1)
                WA1_host=$(awk '/host_bytes_written/ {print $NF}' ${Result_Location}/SW_WA_before_${elem}.log | head -1)
                WA2_host=$(awk '/host_bytes_written/ {print $NF}' ${Result_Location}/SW_WA_after_${elem}.log | head -1)

                #WA1_nand=`cat ${Result_Location}/SW_WA_before_${elem}n1.log |grep nand_bytes_written |awk '{print $x}' x="${WA}"`
                #WA2_nand=`cat ${Result_Location}/SW_WA_after_${elem}n1.log |grep nand_bytes_written |awk '{print $x}' x="${WA}"`
                #WA1_host=`cat ${Result_Location}/SW_WA_before_${elem}n1.log |grep host_bytes_written |awk '{print $x}' x="${WA}"`
                #WA2_host=`cat ${Result_Location}/SW_WA_after_${elem}n1.log |grep host_bytes_written |awk '{print $x}' x="${WA}"`
                t1=$((WA2_nand-WA1_nand))
                echo "t1=$t1"
                t2=$((WA2_host-WA1_host))
                echo "t2=$t2"
                SW_WA=$(echo "scale=4; $t1/$t2" | bc)
                echo "the SW_WA result is:$SW_WA"
           fi

            #增加IO await数据解析
            cat ${Result_Location}/BW_SW_$elem.log | awk '{print $x}' x="${num_Wawait}" >> ${Result_Location}/BW_SWawait_$elem.csv
            sed -i '$d' ${Result_Location}/BW_SWawait_$elem.csv  #删除最后一行
            sed -i '$d' ${Result_Location}/BW_SWawait_$elem.csv  #删除最后一行
            sed -i '$d' ${Result_Location}/BW_SWawait_$elem.csv  #删除最后一行
            tmp=$(cat ${Result_Location}/BW_SWawait_$elem.csv |awk -F "," '{print $1}' |tail -n 100 |awk '{sum+=$1} END {print sum/NR}') #取最后100行的平均值
            tmp_max=$(cat ${Result_Location}/BW_SWawait_$elem.csv |awk -F , '{print $1}' |sort -r |sed -n "2,1p")
            Result_await=$Result_await" SW_await:$tmp "
            Result_await_Max=$Result_await_Max"SW_await_Max:$tmp_max "
        # SR
            echo "SR_$elem" > ${Result_Location}/BW_SR_$elem.csv
            echo "SR_$elem" > ${Result_Location}/BW_SRawait_$elem.csv
        # cat ${Result_Location}/BW_SR.log |grep $elem > ${Result_Location}/BW_SR_$elem.log
            cat ${Result_Location}/BW_SR.log | grep -E "(^|[[:space:]])${elem1}([[:space:]]|$)" > ${Result_Location}/BW_SR_$elem.log
            cat ${Result_Location}/BW_SR_$elem.log | awk '{print $x}' x="${num_SR}" >> ${Result_Location}/BW_SR_$elem.csv
            sed -i '$d' ${Result_Location}/BW_SR_$elem.csv  #删除最后一行
            sed -i '$d' ${Result_Location}/BW_SR_$elem.csv  #删除最后一行
            sed -i '$d' ${Result_Location}/BW_SR_$elem.csv  #删除最后一行
            tmp=$(cat ${Result_Location}/BW_SR_$elem.csv |awk -F "," '{print $1}' |tail -n 100 |awk '{sum+=$1} END {print sum/NR}') #取最后100行的平均值
            Result=$Result" SR:${tmp}   "

            if [[ $elem == nvme* ]];
            then
                Result=$Result"         RW_WA: ${RW_WA}    SW_WA: ${SW_WA} \n"
            else
                Result=$Result" \n"   #为hDD增加换行符
            fi
            #增加IO await数据解析
            cat ${Result_Location}/BW_SR_$elem.log | awk '{print $x}' x="${num_Rawait}" >> ${Result_Location}/BW_SRawait_$elem.csv
            sed -i '$d' ${Result_Location}/BW_SRawait_$elem.csv  #删除最后一行
            sed -i '$d' ${Result_Location}/BW_SRawait_$elem.csv  #删除最后一行
            sed -i '$d' ${Result_Location}/BW_SRawait_$elem.csv  #删除最后一行
            tmp=$(cat ${Result_Location}/BW_SRawait_$elem.csv |awk -F "," '{print $1}' |tail -n 100 |awk '{sum+=$1} END {print sum/NR}') #取最后100行的平均值
            tmp_max=$(cat ${Result_Location}/BW_SRawait_$elem.csv |awk -F , '{print $1}' |sort -r |sed -n "2,1p")
            Result_await=$Result_await" SR_await:$tmp \n"
            Result_await_Max=$Result_await_Max"SR_await_Max:$tmp_max \n"
    done
    paste -d "," ${Result_Location}/IOPS_RR_*.csv > ${Result_Location}/Summary_IOPS_RR.csv
    paste -d "," ${Result_Location}/IOPS_RW_*.csv > ${Result_Location}/Summary_IOPS_RW.csv
    paste -d "," ${Result_Location}/BW_SR_*.csv > ${Result_Location}/Summary_BW_SR.csv
    paste -d "," ${Result_Location}/BW_SW_*.csv > ${Result_Location}/Summary_BW_SW.csv

    paste -d "," ${Result_Location}/tmp_WholeTest_*.csv > ${Result_Location}/Summary_temp.csv

    paste -d "," ${Result_Location}/IOPS_RRawait_*.csv > ${Result_Location}/Summary_IOawait_RR.csv
    paste -d "," ${Result_Location}/IOPS_RWawait_*.csv > ${Result_Location}/Summary_IOawait_RW.csv
    paste -d "," ${Result_Location}/BW_SRawait_*.csv > ${Result_Location}/Summary_IOawait_SR.csv
    paste -d "," ${Result_Location}/BW_SWawait_*.csv > ${Result_Location}/Summary_IOawait_SW.csv

    gen_gnuplot_input ${Result_Location}/Summary_IOPS_RR.csv |gnuplot -persist
    gen_gnuplot_input ${Result_Location}/Summary_IOPS_RW.csv |gnuplot -persist
    gen_gnuplot_input ${Result_Location}/Summary_BW_SR.csv |gnuplot -persist
    gen_gnuplot_input ${Result_Location}/Summary_BW_SW.csv |gnuplot -persist
    gen_gnuplot_input ${Result_Location}/Summary_IOawait_RR.csv |gnuplot -persist
    gen_gnuplot_input ${Result_Location}/Summary_IOawait_RW.csv |gnuplot -persist
    gen_gnuplot_input ${Result_Location}/Summary_IOawait_SR.csv |gnuplot -persist
    gen_gnuplot_input ${Result_Location}/Summary_IOawait_SW.csv |gnuplot -persist

    gen_gnuplot_input ${Result_Location}/Summary_temp.csv |gnuplot -persist

    # 5. 在测试结束后收集smart信息
    for drive in $(echo $para | tr ',' ' '); do
        if [[ $drive == nvme* ]]; then
            nvme smart-log /dev/$drive > ${Result_Location}/smart_after_${drive}.log
        else
            smartctl -a /dev/$drive > ${Result_Location}/smart_after_${drive}.log
        fi
    done

    echo -e $Result > ${Result_Location}/result.log
    echo -e "$Result_await" >> ${Result_Location}/result.log
    echo -e "$Result_await_Max" >> ${Result_Location}/result.log
    generate_report

    cd "${Result_Location}"
    zip -r multiDrivePerformance.zip ./*

#    rm -f ${Result_Location}/tmp_WholeTest_* ${Result_Location}/IOPS_*.csv ${Result_Location}/BW_*.csv ${Result_Location}/IOPS_RW_*.log ${Result_Location}/IOPS_RR_*.log ${Result_Location}/BW_SW_*.log ${Result_Location}/BW_SR_*.log ${Result_Location}/*await_*.csv ${Result_Location}/*W_WA*log  ${Result_Location}/tmp_WholeTest*.log ${Result_Location}/*.out
#    sleep 2s
}
# 修改脚本入口
if [ "${BASH_SOURCE[0]}" = "$0" ]; then
    main "$@"
fi
#输出结果
echo "the test result already tar as multiDrivePerformance.tar, and put at the location of ${Result_Location}."
echo "the summary result in Performance.csv file."

