#!/bin/bash

# 简化的ARM平台Linpack测试脚本
# 专门解决HPL编译问题

export LANG=en_US.UTF-8

# 目录设置
OUTPUT_DIR="/home/<USER>/linpack"
SOFTWARE_DIR="/home/<USER>"
BUILD_DIR="$SOFTWARE_DIR/hpl_build"
LOG_FILE="$OUTPUT_DIR/linpack_run.log"

# 创建目录
mkdir -p "$OUTPUT_DIR" "$SOFTWARE_DIR" "$BUILD_DIR"

echo "=========================================="
echo "ARM平台Linpack测试脚本"
echo "=========================================="

# 检查架构
ARCH=$(uname -m)
if [[ "$ARCH" != "aarch64" && "$ARCH" != "arm64" ]]; then
    echo "[ERROR] 此脚本仅适用于ARM64平台，当前架构: $ARCH"
    exit 1
fi

echo "[INFO] 检测到ARM64平台: $ARCH" | tee -a "$LOG_FILE"

# 修复yum源
fix_yum_repos() {
    echo "[INFO] 修复yum源..." | tee -a "$LOG_FILE"
    if command -v yum >/dev/null 2>&1; then
        # 备份并清空原有 yum 源
        if [ -d /etc/yum.repos.d ]; then
            mkdir -p /etc/yum.repos.d/backup
            mv /etc/yum.repos.d/*.repo /etc/yum.repos.d/backup/ 2>/dev/null
        fi
        if curl -f -o /etc/yum.repos.d/mtos-arm.repo http://************/huzz/DOCKER/tools/mtos-arm.repo 2>>"$LOG_FILE"; then
            echo "[INFO] ARM yum源配置成功" | tee -a "$LOG_FILE"
            yum clean all >/dev/null 2>&1
            yum makecache >/dev/null 2>&1
        else
            echo "[WARNING] yum源配置失败" | tee -a "$LOG_FILE"
        fi
    fi
}

# 安装依赖
install_dependencies() {
    echo "[INFO] 安装编译依赖..." | tee -a "$LOG_FILE"
    
    fix_yum_repos
    
    # 检查并安装必要工具
    local tools="gcc gcc-c++ gcc-gfortran make cmake wget tar bzip2 unzip perl"
    
    if command -v yum >/dev/null 2>&1; then
        yum install -y $tools 2>&1 | tee -a "$LOG_FILE"
    elif command -v apt-get >/dev/null 2>&1; then
        apt-get update && apt-get install -y gcc g++ gfortran make cmake wget tar bzip2 unzip perl build-essential 2>&1 | tee -a "$LOG_FILE"
    else
        echo "[ERROR] 无法识别包管理器" | tee -a "$LOG_FILE"
        exit 1
    fi
    
    # 验证关键工具
    for tool in gcc gfortran make; do
        if ! command -v "$tool" >/dev/null 2>&1; then
            echo "[ERROR] $tool 安装失败" | tee -a "$LOG_FILE"
            exit 1
        fi
    done
    
    echo "[INFO] 依赖安装完成" | tee -a "$LOG_FILE"
}

# 编译OpenMPI
build_openmpi() {
    echo "[INFO] 编译OpenMPI..." | tee -a "$LOG_FILE"
    
    cd "$BUILD_DIR" || exit 1
    
    # 下载OpenMPI
    local openmpi_url="http://************/huzz/DOCKER/tools/openmpi-4.0.7.tar.gz"
    if ! wget --timeout=60 "$openmpi_url" -O openmpi-4.0.7.tar.gz; then
        echo "[ERROR] OpenMPI下载失败" | tee -a "$LOG_FILE"
        exit 1
    fi
    
    tar -zxf openmpi-4.0.7.tar.gz
    cd openmpi-4.0.7 || exit 1
    
    # 配置和编译
    local mpi_prefix="/home/<USER>/OpenMPI"
    rm -rf "$mpi_prefix"
    mkdir -p "$mpi_prefix"
    
    ./configure --prefix="$mpi_prefix" --enable-mpi1-compatibility 2>&1 | tee -a "$LOG_FILE"
    make -j"$(nproc)" 2>&1 | tee -a "$LOG_FILE"
    make install 2>&1 | tee -a "$LOG_FILE"
    
    # 设置环境变量
    export MPI_HOME="$mpi_prefix"
    export PATH="$MPI_HOME/bin:$PATH"
    export LD_LIBRARY_PATH="$MPI_HOME/lib:$LD_LIBRARY_PATH"
    
    # 验证安装
    if command -v mpicc >/dev/null 2>&1; then
        echo "[INFO] OpenMPI安装成功: $(mpicc --version | head -1)" | tee -a "$LOG_FILE"
    else
        echo "[ERROR] OpenMPI安装失败" | tee -a "$LOG_FILE"
        exit 1
    fi
}

# 编译OpenBLAS
build_openblas() {
    echo "[INFO] 编译OpenBLAS..." | tee -a "$LOG_FILE"
    
    cd "$BUILD_DIR" || exit 1
    
    # 下载OpenBLAS
    local openblas_url="http://************/huzz/DOCKER/tools/OpenBLAS-0.3.6.zip"
    if ! wget --timeout=60 "$openblas_url" -O OpenBLAS-0.3.6.zip; then
        echo "[ERROR] OpenBLAS下载失败" | tee -a "$LOG_FILE"
        exit 1
    fi
    
    unzip -o -q OpenBLAS-0.3.6.zip
    cd OpenBLAS-0.3.6 || exit 1
    
    # 编译和安装
    make -j"$(nproc)" TARGET=ARMV8 2>&1 | tee -a "$LOG_FILE"
    
    local openblas_prefix="/home/<USER>/OpenBLAS"
    rm -rf "$openblas_prefix"
    make install PREFIX="$openblas_prefix" 2>&1 | tee -a "$LOG_FILE"
    
    # 设置环境变量
    export BLAS_LIB="$openblas_prefix/lib"
    export BLAS_FLAGS="-L$openblas_prefix/lib -lopenblas"
    export LD_LIBRARY_PATH="$BLAS_LIB:$LD_LIBRARY_PATH"
    
    echo "[INFO] OpenBLAS安装成功" | tee -a "$LOG_FILE"
}

# 编译HPL
build_hpl() {
    echo "[INFO] 编译HPL..." | tee -a "$LOG_FILE"
    
    cd "$BUILD_DIR" || exit 1
    
    # 下载HPL
    local hpl_url="http://************/huzz/DOCKER/tools/hpl-2.3.tar.gz"
    if ! wget --timeout=60 "$hpl_url" -O hpl-2.3.tar.gz; then
        echo "[ERROR] HPL下载失败" | tee -a "$LOG_FILE"
        exit 1
    fi
    
    # 清理并解压
    rm -rf hpl-2.3
    tar -zxf hpl-2.3.tar.gz
    cd hpl-2.3 || exit 1
    
    # 创建Make配置
    cat > Make.Linux_ARM << EOF
SHELL        = /bin/sh
CD           = cd
CP           = cp
LN_S         = ln -fs
MKDIR        = mkdir -p
RM           = /bin/rm -f
TOUCH        = touch
ARCH         = Linux_ARM
TOPdir       = $(pwd)
INCdir       = \$(TOPdir)/include
BINdir       = \$(TOPdir)/bin/\$(ARCH)
LIBdir       = \$(TOPdir)/lib/\$(ARCH)
HPLlib       = \$(LIBdir)/libhpl.a
MPdir        = $MPI_HOME
MPinc        = -I\$(MPdir)/include
MPlib        = -L\$(MPdir)/lib -lmpi
LAdir        = $BLAS_LIB
LAinc        = 
LAlib        = $BLAS_FLAGS
F2CDEFS      = -DAdd__ -DF77_INTEGER=int -DStringSunStyle
HPL_OPTS     = -DHPL_CALL_CBLAS
HPL_INCLUDES = -I\$(INCdir) -I\$(INCdir)/\$(ARCH) \$(LAinc) \$(MPinc)
HPL_LIBS     = \$(HPLlib) \$(LAlib) \$(MPlib)
HPL_DEFS     = \$(F2CDEFS) \$(HPL_OPTS) \$(HPL_INCLUDES)
CC           = mpicc
CCNOOPT      = \$(HPL_DEFS)
CCFLAGS      = \$(HPL_DEFS) -fomit-frame-pointer -O3 -funroll-loops
LINKER       = mpicc
LINKFLAGS    = \$(CCFLAGS)
ARCHIVER     = ar
ARFLAGS      = r
RANLIB       = echo
EOF
    
    # 清理并编译
    make clean arch=Linux_ARM 2>/dev/null || true
    
    # 创建目录结构
    mkdir -p bin/Linux_ARM lib/Linux_ARM include/Linux_ARM
    
    # 设置Make.inc链接
    find . -type d -name Linux_ARM | while IFS= read -r dir; do
        ln -fs ../../../Make.Linux_ARM "$dir/Make.inc" 2>/dev/null || true
    done
    
    # 编译
    echo "[INFO] 开始HPL编译，这可能需要几分钟..." | tee -a "$LOG_FILE"
    if timeout 1800 make arch=Linux_ARM 2>&1 | tee -a "$LOG_FILE"; then
        echo "[INFO] HPL编译成功" | tee -a "$LOG_FILE"
    else
        echo "[WARNING] 标准编译失败，尝试分步编译..." | tee -a "$LOG_FILE"
        
        # 分步编译
        make src_lib arch=Linux_ARM 2>&1 | tee -a "$LOG_FILE"
        make src_test arch=Linux_ARM 2>&1 | tee -a "$LOG_FILE"
    fi
    
    # 检查结果
    if [ -f "bin/Linux_ARM/xhpl" ]; then
        echo "[INFO] HPL可执行文件生成成功" | tee -a "$LOG_FILE"
        
        # 复制到linpack目录
        local linpack_dir="$SOFTWARE_DIR/linpack"
        mkdir -p "$linpack_dir"
        cp bin/Linux_ARM/xhpl "$linpack_dir/"
        chmod +x "$linpack_dir/xhpl"
        
        # 创建HPL.dat配置文件
        cat > "$linpack_dir/HPL.dat" << 'EOF'
HPLinpack benchmark input file
Innovative Computing Laboratory, University of Tennessee
HPL.out      output file name (if any)
6            device out (6=stdout,7=stderr,file)
1            # of problems sizes (N)
20000        Ns
1            # of NBs
192          NBs
0            PMAP process mapping (0=Row-,1=Column-major)
1            # of process grids (P x Q)
2            Ps
4            Qs
16.0         threshold
1            # of panel fact
2            PFACTs (0=left, 1=Crout, 2=Right)
1            # of recursive stopping criterium
4            NBMINs (>= 1)
1            # of panels in recursion
2            NDIVs
1            # of recursive panel fact.
2            RFACTs (0=left, 1=Crout, 2=Right)
1            # of broadcast
1            BCASTs (0=1rg,1=1rM,2=2rg,3=2rM,4=Lng,5=LnM)
1            # of lookahead depth
1            DEPTHs (>=0)
2            SWAP (0=bin-exch,1=long,2=mix)
64           swapping threshold
0            L1 in (0=transposed,1=no-transposed) form
0            U  in (0=transposed,1=no-transposed) form
1            Equilibration (0=no,1=yes)
8            memory alignment in double (> 0)
EOF
        
        echo "[INFO] HPL安装完成: $linpack_dir/xhpl" | tee -a "$LOG_FILE"
        return 0
    else
        echo "[ERROR] HPL编译失败，未生成可执行文件" | tee -a "$LOG_FILE"
        return 1
    fi
}

# 运行测试
run_test() {
    echo "[INFO] 运行HPL测试..." | tee -a "$LOG_FILE"
    
    local linpack_dir="$SOFTWARE_DIR/linpack"
    cd "$linpack_dir" || exit 1
    
    # 设置环境变量
    export MPI_HOME="/home/<USER>/OpenMPI"
    export PATH="$MPI_HOME/bin:$PATH"
    export LD_LIBRARY_PATH="$MPI_HOME/lib:/home/<USER>/OpenBLAS/lib:$LD_LIBRARY_PATH"
    
    # 运行测试
    echo "[INFO] 开始执行HPL基准测试..." | tee -a "$LOG_FILE"
    if timeout 1800 mpirun -np 8 --allow-run-as-root ./xhpl > "$OUTPUT_DIR/linpack_full.log" 2>&1; then
        echo "[INFO] HPL测试完成" | tee -a "$LOG_FILE"
        
        # 提取结果
        if grep -q "WR.*L.*L" "$OUTPUT_DIR/linpack_full.log"; then
            local result=$(grep "WR.*L.*L" "$OUTPUT_DIR/linpack_full.log" | tail -1 | awk '{print $NF}')
            local gflops=$(echo "$result" | awk '{printf "%.4f", $1}')
            
            echo "Average: $gflops" > "$OUTPUT_DIR/linpack.log"
            echo "Max: $gflops" >> "$OUTPUT_DIR/linpack.log"
            
            echo "[INFO] 测试结果: $gflops GFlops" | tee -a "$LOG_FILE"
            
            # 生成报告
            cat > "$OUTPUT_DIR/linpack_report.md" << EOF
# Linpack 测试报告

## 测试环境
- **测试时间**: $(date '+%Y-%m-%d %H:%M:%S')
- **测试平台**: ARM64 ($ARCH)
- **CPU型号**: $(lscpu | grep "Model name" | sed 's/Model name: *//g' || echo "Unknown")
- **CPU核心数**: $(nproc)
- **测试工具**: HPL (High Performance Linpack)

## 测试结果
- **性能**: $gflops GFlops

## 说明
HPL通过求解大型线性方程组测试CPU浮点运算性能。
GFlops值越高表示浮点计算性能越好。
EOF
            
            echo "[INFO] 测试报告已生成: $OUTPUT_DIR/linpack_report.md" | tee -a "$LOG_FILE"
        else
            echo "[WARNING] 无法从测试输出中提取结果" | tee -a "$LOG_FILE"
        fi
    else
        echo "[ERROR] HPL测试执行失败" | tee -a "$LOG_FILE"
        return 1
    fi
}

# 主函数
main() {
    echo "[INFO] 开始ARM平台Linpack测试流程..." | tee -a "$LOG_FILE"
    
    install_dependencies
    build_openmpi
    build_openblas
    
    if build_hpl; then
        run_test
        echo "[INFO] Linpack测试流程完成" | tee -a "$LOG_FILE"
    else
        echo "[ERROR] HPL编译失败，无法进行测试" | tee -a "$LOG_FILE"
        exit 1
    fi
}

# 执行主函数
main
