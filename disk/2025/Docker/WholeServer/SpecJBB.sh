#!/bin/bash
export LANG=en_US.UTF-8
JAVA=java
# 1. 目录与变量定义
OUTPUT_DIR="/home/<USER>/SpecJBB"
SOFTWARE_DIR="/home/<USER>/SpecJBB"
LOG_FILE="$OUTPUT_DIR/specjbb_run.log"
RESULT_LOG="$OUTPUT_DIR/specjbb.log"
mkdir -p "$OUTPUT_DIR" "$SOFTWARE_DIR"

arch=$(uname -m)

# 强制重置yum源
echo "[INFO] 重置yum源..."
rm -rf /etc/yum.repos.d/*

if [[ "$arch" == "aarch64" ]]; then
    # ARM平台
    echo "[INFO] 检测到 ARM 架构，使用本地mtos-arm.repo"
    curl -o /etc/yum.repos.d/mtos-arm.repo http://************/huzz/DOCKER/tools/mtos-arm.repo
else
    # x86平台
    echo "[INFO] 检测到 x86 架构，使用阿里云CentOS 7源"
    curl -o /etc/yum.repos.d/CentOS-Base.repo  http://************/huzz/DOCKER/tools/Centos-7.repo
fi

# 2. 自动安装依赖
echo "[INFO] 安装依赖..."
yum install -y wget unzip java-11-openjdk-devel which

# 3. 自动下载并解压 SpecJBB
if [ ! -f "$SOFTWARE_DIR/SpecJBB.zip" ]; then
    echo "[INFO] 下载 SpecJBB..."
    wget -O "$SOFTWARE_DIR/SpecJBB.zip" "http://************/333/SpecJBB.zip"
fi
if [ ! -f "$SOFTWARE_DIR/specjbb2015.jar" ]; then
    unzip -o "$SOFTWARE_DIR/SpecJBB.zip" -d "$SOFTWARE_DIR"
    # 如果解压后出现了$SOFTWARE_DIR/SpecJBB目录，则将其内容移到$SOFTWARE_DIR并删除多余目录
    if [ -d "$SOFTWARE_DIR/SpecJBB" ]; then
        mv "$SOFTWARE_DIR/SpecJBB"/* "$SOFTWARE_DIR" 2>/dev/null || true
        mv "$SOFTWARE_DIR/SpecJBB"/.[!.]* "$SOFTWARE_DIR" 2>/dev/null || true
        rmdir "$SOFTWARE_DIR/SpecJBB"
    fi
fi

# 4. 清理历史结果（只清理结果目录，不删安装包内容）
rm -rf "$OUTPUT_DIR"/*
rm -rf "$SOFTWARE_DIR/result"

# ========== 支持 test 快速测试参数 ==========
MODE=${1:-ref}
if [[ "$MODE" == "test" ]]; then
    echo "[INFO] 进入SpecJBB快速测试模式"
    GROUP_COUNT=2
    JAVA_OPTS="-Xms2g -Xmx2g -Xmn1g -XX:+UseG1GC"
    MODE_ARGS="-skipReport"
fi

# 5. 平台识别与参数优化
PLATFORM="unknown"
arch=$(uname -m)
if lscpu | grep -qi 'GenuineIntel'; then
    PLATFORM="intel"
elif lscpu | grep -qi 'AuthenticAMD'; then
    PLATFORM="amd"
elif lscpu | grep -qi 'Kunpeng'; then
    PLATFORM="kunpeng"
elif [[ "$arch" == "aarch64" ]]; then
    PLATFORM="arm"
fi

PHYS_CORES=$(lscpu | awk -F: '/Core.s. per socket/ {cps=$2} /Socket.s/ {sks=$2} END {print cps*sks}' | xargs)
[ -z "$PHYS_CORES" ] && PHYS_CORES=$(nproc)

# 6. 自动修改run_multi.sh参数
RUN_MULTI_SH="$SOFTWARE_DIR/run_multi.sh"
if [ -f "$RUN_MULTI_SH" ]; then
    cp "$RUN_MULTI_SH" "$RUN_MULTI_SH.bak"
    # 平台自适应参数
    if [[ "$MODE" == "test" ]]; then
        sed -i "s/^GROUP_COUNT=.*/GROUP_COUNT=1/" "$RUN_MULTI_SH"
        sed -i "s|^JAVA_OPTS_C=.*|JAVA_OPTS_C=\"-Xms1g -Xmx1g -Xmn512m -XX:+UseParallelGC\"|" "$RUN_MULTI_SH"
        sed -i "s|^JAVA_OPTS_TI=.*|JAVA_OPTS_TI=\"-Xms1g -Xmx1g -Xmn512m -XX:+UseParallelGC\"|" "$RUN_MULTI_SH"
        sed -i "s|^JAVA_OPTS_BE=.*|JAVA_OPTS_BE=\"-Xms1g -Xmx1g -Xmn512m -XX:+UseParallelGC\"|" "$RUN_MULTI_SH"
    else
        if [[ "$PLATFORM" == "intel" ]]; then
            GROUP_COUNT=2
            JAVA_OPTS_C="-Xms4g -Xmx4g -Xmn2g -XX:+UseParallelGC -XX:+UseLargePages -XX:+AlwaysPreTouch -XX:+UseNUMA -XX:ParallelGCThreads=$PHYS_CORES -XX:ConcGCThreads=$(($PHYS_CORES/2)) -XX:UseAVX=2"
            JAVA_OPTS_TI="$JAVA_OPTS_C"
            JAVA_OPTS_BE="-Xms32g -Xmx32g -Xmn16g -XX:+UseParallelGC -XX:+UseLargePages -XX:+AlwaysPreTouch -XX:+UseNUMA -XX:ParallelGCThreads=$PHYS_CORES -XX:ConcGCThreads=$(($PHYS_CORES/2)) -XX:UseAVX=2"
            sed -i "s/^GROUP_COUNT=.*/GROUP_COUNT=$GROUP_COUNT/" "$RUN_MULTI_SH"
            sed -i "s|^JAVA_OPTS_C=.*|JAVA_OPTS_C=\"$JAVA_OPTS_C\"|" "$RUN_MULTI_SH"
            sed -i "s|^JAVA_OPTS_TI=.*|JAVA_OPTS_TI=\"$JAVA_OPTS_TI\"|" "$RUN_MULTI_SH"
            sed -i "s|^JAVA_OPTS_BE=.*|JAVA_OPTS_BE=\"$JAVA_OPTS_BE\"|" "$RUN_MULTI_SH"
        elif [[ "$PLATFORM" == "amd" ]]; then
            GROUP_COUNT=2
            JAVA_OPTS_C="-Xms4g -Xmx4g -Xmn2g -XX:+UseParallelGC -XX:+UseLargePages -XX:+AlwaysPreTouch -XX:+UseNUMA -XX:ParallelGCThreads=$PHYS_CORES -XX:ConcGCThreads=$(($PHYS_CORES/2))"
            JAVA_OPTS_TI="$JAVA_OPTS_C"
            JAVA_OPTS_BE="-Xms32g -Xmx32g -Xmn16g -XX:+UseParallelGC -XX:+UseLargePages -XX:+AlwaysPreTouch -XX:+UseNUMA -XX:ParallelGCThreads=$PHYS_CORES -XX:ConcGCThreads=$(($PHYS_CORES/2))"
            sed -i "s/^GROUP_COUNT=.*/GROUP_COUNT=$GROUP_COUNT/" "$RUN_MULTI_SH"
            sed -i "s|^JAVA_OPTS_C=.*|JAVA_OPTS_C=\"$JAVA_OPTS_C\"|" "$RUN_MULTI_SH"
            sed -i "s|^JAVA_OPTS_TI=.*|JAVA_OPTS_TI=\"$JAVA_OPTS_TI\"|" "$RUN_MULTI_SH"
            sed -i "s|^JAVA_OPTS_BE=.*|JAVA_OPTS_BE=\"$JAVA_OPTS_BE\"|" "$RUN_MULTI_SH"
        elif [[ "$PLATFORM" == "kunpeng" ]]; then
            GROUP_COUNT=8
            TI_JVM_COUNT=1
            SPEC_OPTS_C="-Dspecjbb.group.count=$GROUP_COUNT -Dspecjbb.txi.pergroup.count=$TI_JVM_COUNT -Dspecjbb.forkjoin.workers=64"
            JAVA_OPTS_C="-server -Xms2g -Xmx2g -Xmn1536m -XX:+UseG1GC -XX:+UnlockExperimentalVMOptions -XX:+UseFastSerializer -DfastSerializerEscapeMode=true"
            JAVA_OPTS_TI="$JAVA_OPTS_C"
            JAVA_OPTS_BE="-server -Xmx29g -Xms29g -Xmn28g -XX:+UseG1GC -XX:ParallelGCThreads=32 -XX:+UseBiasedLocking -XX:+AlwaysPreTouch -XX:-UseAdaptiveSizePolicy -XX:MaxTenuringThreshold=15 -XX:AutoBoxCacheMax=20000 -XX:InlineSmallCode=10k -XX:+UnlockExperimentalVMOptions -XX:+UseFastSerializer -DfastSerializerEscapeMode=true -XX:+UnlockExperimentalVMOptions -XX:+UseNUMA"
            MODE_ARGS_C="-ikv"
            MODE_ARGS_TI="-ikv"
            MODE_ARGS_BE="-ikv"
            sed -i "s/^GROUP_COUNT=.*/GROUP_COUNT=$GROUP_COUNT/" "$RUN_MULTI_SH"
            sed -i "s|^JAVA_OPTS_C=.*|JAVA_OPTS_C=\"$JAVA_OPTS_C\"|" "$RUN_MULTI_SH"
            sed -i "s|^JAVA_OPTS_TI=.*|JAVA_OPTS_TI=\"$JAVA_OPTS_TI\"|" "$RUN_MULTI_SH"
            sed -i "s|^JAVA_OPTS_BE=.*|JAVA_OPTS_BE=\"$JAVA_OPTS_BE\"|" "$RUN_MULTI_SH"
            sed -i "s|^SPEC_OPTS_C=.*|SPEC_OPTS_C=\"$SPEC_OPTS_C\"|" "$RUN_MULTI_SH"
            sed -i "s|^MODE_ARGS_C=.*|MODE_ARGS_C=\"$MODE_ARGS_C\"|" "$RUN_MULTI_SH"
            sed -i "s|^MODE_ARGS_TI=.*|MODE_ARGS_TI=\"$MODE_ARGS_TI\"|" "$RUN_MULTI_SH"
            sed -i "s|^MODE_ARGS_BE=.*|MODE_ARGS_BE=\"$MODE_ARGS_BE\"|" "$RUN_MULTI_SH"
        else
            GROUP_COUNT=2
            JAVA_OPTS_C="-Xms2g -Xmx2g -Xmn1g -XX:+UseParallelGC"
            JAVA_OPTS_TI="$JAVA_OPTS_C"
            JAVA_OPTS_BE="-Xms8g -Xmx8g -Xmn4g -XX:+UseParallelGC"
            sed -i "s/^GROUP_COUNT=.*/GROUP_COUNT=$GROUP_COUNT/" "$RUN_MULTI_SH"
            sed -i "s|^JAVA_OPTS_C=.*|JAVA_OPTS_C=\"$JAVA_OPTS_C\"|" "$RUN_MULTI_SH"
            sed -i "s|^JAVA_OPTS_TI=.*|JAVA_OPTS_TI=\"$JAVA_OPTS_TI\"|" "$RUN_MULTI_SH"
            sed -i "s|^JAVA_OPTS_BE=.*|JAVA_OPTS_BE=\"$JAVA_OPTS_BE\"|" "$RUN_MULTI_SH"
        fi
    fi
fi

# 7. 执行run_multi.sh
cd "$SOFTWARE_DIR"
bash run_multi.sh

# 8. 结果与报告生成逻辑保持不变
# 只杀死本目录下的 Java 进程
ps -ef | grep "[s]pecjbb2015.jar" | awk '{print $2}' | xargs -r kill -9

# 统一结果目录
result_dir="$SOFTWARE_DIR/result"
mkdir "$result_dir"

# 自动查找config目录
CONFIG_SRC=$(find "$SOFTWARE_DIR" -type d -name config | head -1)
if [ -n "$CONFIG_SRC" ] && [ -d "$CONFIG_SRC" ]; then
    cp -r "$CONFIG_SRC" "$result_dir"
else
    echo "[ERROR] 未找到config目录，请检查SpecJBB安装包内容！" | tee -a "$LOG_FILE"
    ls -lR "$SOFTWARE_DIR"   # 增加递归列出所有内容，便于排查
fi

cd "$result_dir"

# ================== 平台识别与参数优化 ==================
PLATFORM="unknown"
arch=$(uname -m)
if lscpu | grep -qi 'GenuineIntel'; then
    PLATFORM="intel"
elif lscpu | grep -qi 'AuthenticAMD'; then
    PLATFORM="amd"
elif lscpu | grep -qi 'Kunpeng'; then
    PLATFORM="kunpeng"
elif [[ "$arch" == "aarch64" ]]; then
    PLATFORM="arm"
fi

# 获取物理核心数
PHYS_CORES=$(lscpu | awk -F: '/Core.s. per socket/ {cps=$2} /Socket.s/ {sks=$2} END {print cps*sks}' | xargs)
[ -z "$PHYS_CORES" ] && PHYS_CORES=$(nproc)

# 参数初始化
SPEC_OPTS=""
JAVA_OPTS="-server -Xms32g -Xmx32g -Xmn16g -XX:+UseG1GC -XX:+UnlockExperimentalVMOptions -XX:+UseFastSerializer -DfastSerializerEscapeMode=true -XX:ParallelGCThreads=$PHYS_CORES -XX:+UseBiasedLocking -XX:+AlwaysPreTouch -XX:-UseAdaptiveSizePolicy -XX:MaxTenuringThreshold=15 -XX:AutoBoxCacheMax=20000 -XX:InlineSmallCode=10k -XX:+UseNUMA"
MODE_ARGS="-skipReport"

if [[ "$PLATFORM" == "intel" ]]; then
    SPEC_OPTS="-Dspecjbb.group.count=2"
    JAVA_OPTS="-Xms32g -Xmx32g -Xmn16g -XX:+UseParallelGC -XX:+UseLargePages -XX:+AlwaysPreTouch -XX:+UseNUMA -XX:ParallelGCThreads=$PHYS_CORES -XX:ConcGCThreads=$(($PHYS_CORES/2)) -XX:UseAVX=2"
elif [[ "$PLATFORM" == "amd" ]]; then
    SPEC_OPTS="-Dspecjbb.group.count=2"
    JAVA_OPTS="-Xms32g -Xmx32g -Xmn16g -XX:+UseParallelGC -XX:+UseLargePages -XX:+AlwaysPreTouch -XX:+UseNUMA -XX:ParallelGCThreads=$PHYS_CORES -XX:ConcGCThreads=$(($PHYS_CORES/2))"
elif [[ "$PLATFORM" == "kunpeng" ]]; then
    # 鲲鹏ARM优化参数，参考runmuilti_specjbb_kunpeng.sh
    GROUP_COUNT=8
    TI_JVM_COUNT=1
    SPEC_OPTS="-Dspecjbb.group.count=$GROUP_COUNT -Dspecjbb.txi.pergroup.count=$TI_JVM_COUNT -Dspecjbb.forkjoin.workers=64"
    JAVA_OPTS="-server -Xms32g -Xmx32g -Xmn16g -XX:+UseG1GC -XX:+UnlockExperimentalVMOptions -XX:+UseFastSerializer -DfastSerializerEscapeMode=true -XX:ParallelGCThreads=$PHYS_CORES -XX:+UseBiasedLocking -XX:+AlwaysPreTouch -XX:-UseAdaptiveSizePolicy -XX:MaxTenuringThreshold=15 -XX:AutoBoxCacheMax=20000 -XX:InlineSmallCode=10k -XX:+UseNUMA"
    MODE_ARGS="-ikv -skipReport"
elif [[ "$PLATFORM" == "arm" ]]; then
    # 其他ARM平台，采用通用ARM优化
    SPEC_OPTS="-Dspecjbb.group.count=2"
    JAVA_OPTS="-Xms32g -Xmx32g -Xmn16g -XX:+UseG1GC -XX:+UseNUMA -XX:+AlwaysPreTouch -XX:ParallelGCThreads=$PHYS_CORES"
    MODE_ARGS="-skipReport"
else
    # 未知平台，使用保守参数
    SPEC_OPTS="-Dspecjbb.group.count=2"
    JAVA_OPTS="-Xms16g -Xmx16g -Xmn8g -XX:+UseParallelGC"
    MODE_ARGS="-skipReport"
fi

echo "[INFO] 启动 SPECjbb2015... 平台: $PLATFORM, JAVA_OPTS: $JAVA_OPTS, SPEC_OPTS: $SPEC_OPTS, MODE_ARGS: $MODE_ARGS" | tee "$LOG_FILE"
$JAVA $JAVA_OPTS $SPEC_OPTS -jar ../specjbb2015.jar -m COMPOSITE $MODE_ARGS 2>>"$LOG_FILE" >>"$LOG_FILE" &
JBB_PID=$!
wait $JBB_PID

echo "[INFO] SPECjbb2015 测试完成" | tee -a "$LOG_FILE"

# ================== 帮助信息 ==================
if [[ "$1" == "-h" || "$1" == "--help" ]]; then
    cat <<EOF
============================================================
脚本名称: SpecJBB.sh

主要功能: 自动化完成SpecJBB2015的依赖安装、测试执行及结果输出，适用于容器环境。

用法:
  bash SpecJBB.sh

参数:
  -h, --help    打印本帮助信息

结果输出:
  - 所有结果、日志、报告均输出到 /home/<USER>/SpecJBB 目录
  - 自动生成 specjbb_report.md 测试报告
============================================================
EOF
    exit 0
fi

# 优先查找最新的带时间戳目录
latest_dir=$(find "$SOFTWARE_DIR" -maxdepth 1 -type d -regex ".*/[0-9][0-9]-[0-9][0-9]-[0-9][0-9]_[0-9][0-9][0-9][0-9][0-9][0-9]" | sort | tail -1)

if [ -n "$latest_dir" ]; then
    # 在最新目录下查找 .data.gz 或 .data
    data_file=$(find "$latest_dir" -type f -name "*.data.gz" | sort | tail -1)
    if [ -z "$data_file" ]; then
        data_file=$(find "$latest_dir" -type f -name "*.data" | sort | tail -1)
        if [ -f "$data_file" ]; then
            gzip -f "$data_file" 2>/dev/null || true
            data_file="${data_file}.gz"
        fi
    fi
    # 查找 reporter.out
    latest_reporter_out=$(find "$latest_dir" -type f -name "*-reporter.out" | sort | tail -1)
else
    # 兜底：全局递归查找
    data_file=$(find "$SOFTWARE_DIR" -type f -name "*.data.gz" | sort | tail -1)
    if [ -z "$data_file" ]; then
        data_file=$(find "$SOFTWARE_DIR" -type f -name "*.data" | sort | tail -1)
        if [ -f "$data_file" ]; then
            gzip -f "$data_file" 2>/dev/null || true
            data_file="${data_file}.gz"
        fi
    fi
    latest_reporter_out=$(find "$SOFTWARE_DIR" -type f -name "*-reporter.out" | sort | tail -1)
fi

if [ -f "$data_file" ]; then
    cp "$data_file" "$SOFTWARE_DIR/result/"
    JAVA_OPTS_REPORTER="-Xms2g -Xmx2g"
    java $JAVA_OPTS_REPORTER -jar "$SOFTWARE_DIR/specjbb2015.jar" -m REPORTER -s "$SOFTWARE_DIR/result/$(basename "$data_file")" -t "$SOFTWARE_DIR/result"
fi

if [ -f "$latest_reporter_out" ]; then
    cp "$latest_reporter_out" "$SOFTWARE_DIR/result/"
fi

# 7. 生成md格式报告
REPORT_MD="$OUTPUT_DIR/specjbb_report.md"
echo "# SpecJBB2015 测试报告" > "$REPORT_MD"
echo "- 测试时间: $(date '+%Y-%m-%d %H:%M:%S')" >> "$REPORT_MD"
echo "- 结果日志: specjbb_run.log" >> "$REPORT_MD"
echo "- 关键结果: " >> "$REPORT_MD"

# 提取 max-jOPS 和 critical-jOPS
if [ -f "$SOFTWARE_DIR/result/$(basename "$latest_reporter_out")" ]; then
    max_jops_line=$(grep 'jOPS' "$SOFTWARE_DIR/result/$(basename "$latest_reporter_out")")
    if [ -n "$max_jops_line" ]; then
        echo "  - $max_jops_line" >> "$REPORT_MD"
    else
        echo "  - 未找到 max-jOPS 结果" >> "$REPORT_MD"
    fi
else
    echo "  - 未找到结果文件" >> "$REPORT_MD"
fi

echo "[INFO] Markdown报告已生成: $REPORT_MD"

echo "[INFO] 结果已输出到 $LOG_FILE"
cat "$LOG_FILE"
cat "$REPORT_MD"

# 9. 清理临时文件
#rm -rf "$SOFTWARE_DIR/result/config"
#rm -rf "$SOFTWARE_DIR/result/config"

