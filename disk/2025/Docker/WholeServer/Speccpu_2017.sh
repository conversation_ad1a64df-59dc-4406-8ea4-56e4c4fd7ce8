#!/bin/bash
export LANG=en_US.UTF-8

# ========== 新增：浮点测试环境优化 ==========
export OMP_STACKSIZE=1G
ulimit -s unlimited

# ========== 新增：自动执行setup.sh初始化环境 ===========
SCRIPT_DIR=$(cd "$(dirname "$0")" && pwd)
if [ -f "$SCRIPT_DIR/setup.sh" ]; then
    echo "[INFO] 正在执行环境初始化脚本 setup.sh ..."
    bash "$SCRIPT_DIR/setup.sh"
    if [ $? -ne 0 ]; then
        echo "[ERROR] setup.sh 执行失败，终止后续操作。"
        exit 1
    fi
    echo "[INFO] setup.sh 执行完成。"
else
    echo "[WARN] 未找到 setup.sh，跳过环境初始化。"
fi

# 日志函数
log_info()  { echo -e "\033[32m[INFO]\033[0m $1"; }
log_warn()  { echo -e "\033[33m[WARN]\033[0m $1"; }
log_error() { echo -e "\033[31m[ERROR]\033[0m $1"; }

# ================== 变量定义 ==================
OUTPUT_DIR="/home/<USER>/SpecCPU2017"
SPECCPU_DIR="/opt/speccpu2017"
CONFIG_NAME="cpu2017-gcc10.5-x86.cfg"
CONFIG_PATH="$SPECCPU_DIR/config/$CONFIG_NAME"
LOG_FILE="$OUTPUT_DIR/speccpu_install.log"

# ========== 新增：清理历史测试数据 ===========
RESULT_DIR="$SPECCPU_DIR/result"
if [ -d "$RESULT_DIR" ]; then
    rm -rf "$RESULT_DIR"/*
    log_info "已清理历史测试数据：$RESULT_DIR"
else
    mkdir -p "$RESULT_DIR"
fi

# ========== 新增：最终输出目录 ===========
FINAL_OUTDIR="/home/<USER>/SpecCPU2017"
FINAL_OUTFILE="$FINAL_OUTDIR/speccpu.log"
mkdir -p "$FINAL_OUTDIR"

# ====== 新增：根据内核版本判断yum源配置 ======
kernel_version=$(uname -r | awk -F. '{print $1"."$2}')
kernel_major=$(echo $kernel_version | awk -F. '{print $1}')
kernel_minor=$(echo $kernel_version | awk -F. '{print $2}')
arch=$(uname -m)

if [[ $kernel_major -gt 6 ]] || ([[ $kernel_major -eq 6 ]] && [[ $kernel_minor -ge 6 ]]); then
    echo "[INFO] 检测到内核版本 >= 6.6，使用CentOS-Base.repo"
    rm -rf /etc/yum.repos.d/*
    curl -o /etc/yum.repos.d/CentOS-Base.repo http://10.8.104.100/huzz/DOCKER/tools/CentOS-Base.repo
    yum clean all 2>/dev/null || true
    yum makecache fast 2>/dev/null || yum makecache 2>/dev/null || true
    yum install -y epel-release 2>/dev/null || true
else
    echo "[INFO] 检测到内核版本 < 6.6，按架构选择yum源"
    rm -rf /etc/yum.repos.d/*
    if [[ "$arch" == "aarch64" ]]; then
        echo "[INFO] 检测到 ARM 架构，使用本地mtos-arm.repo"
        curl -o /etc/yum.repos.d/mtos-arm.repo http://10.8.104.100/huzz/DOCKER/tools/mtos-arm.repo
    else
        echo "[INFO] 检测到 x86 架构，使用mtos源"
        curl -o /etc/yum.repos.d/mtos.repo http://10.8.104.100/huzz/DOCKER/tools/mtos.repo
    fi
    yum clean all 2>/dev/null || true
    yum makecache fast 2>/dev/null || yum makecache 2>/dev/null || true
fi

# ================== 帮助信息 ==================
if [[ "$1" == "-h" || "$1" == "--help" ]]; then
    cat <<EOF
============================================================
脚本名称: Speccpu_2017.sh

主要功能: 自动化完成SpecCPU2017的环境检测、配置文件自动修改、测试执行及结果输出。

用法:
  bash Speccpu_2017.sh [test|ref]

参数:
  -h, --help    打印本帮助信息

结果输出:
  - 所有结果、日志、报告均输出到 /home/<USER>
  - 自动生成 speccpu_report.md 测试报告
============================================================
EOF
    exit 0
fi

# ================== 输出目录准备 ==================
if [ ! -d "$OUTPUT_DIR" ]; then
    mkdir -p "$OUTPUT_DIR" || { log_error "创建输出目录失败"; exit 1; }
fi
chmod 777 "$OUTPUT_DIR"
rm -rf "$OUTPUT_DIR"/*

# ================== 检查gcc ==================
# 检查gcc
gcc -v || { log_error "gcc未正确安装，脚本退出"; exit 1; }

# ================== 平台识别 ==================
arch=$(uname -m)
platform="unknown"
if lscpu | grep -qi 'GenuineIntel'; then
    platform="intel"
elif lscpu | grep -qi 'AuthenticAMD'; then
    platform="amd"
elif lscpu | grep -qi 'HygonGenuine'; then
    platform="hygon"
else
    if [[ "$arch" == "aarch64" ]]; then
        platform="arm"
    fi
fi

if [[ "$platform" == "hygon" ]]; then
    log_info "检测到平台: 海光 (Hygon, $arch)，后续将按AMD平台流程处理。"
    platform="amd"
else
    log_info "检测到平台: $platform ($arch)"
fi

# ================== 自动修改config文件 ==================
cd "$SPECCPU_DIR/config"

# 自动区分物理核心数和逻辑核心数
phys_cores=$(lscpu | awk -F: '/Core.s. per socket/ {cps=$2} /Socket.s/ {sks=$2} END {print cps*sks}' | xargs)
log_cores=$(lscpu | awk -F: '/^CPU.s/ {print $2}' | xargs)

# 自动替换 build_ncpus 的值（用物理核心数）
sed -i "s/^%define[[:space:]]\+build_ncpus[[:space:]]\+[0-9]\+/%define build_ncpus $phys_cores/" "$CONFIG_NAME"

# intrate,fprate: copies
# intspeed,fpspeed: threads
# intrate copies=逻辑核心数，fprate copies=物理核心数
sed -i "/^intrate,fprate:/,/^intspeed,fpspeed:/s/^\( *copies *\)=.*/   copies           = $log_cores/" "$CONFIG_NAME"
sed -i "/^intspeed,fpspeed:/,/^#------- Compilers/s/^\( *threads *\)=.*/   threads          = $phys_cores/" "$CONFIG_NAME"

# 设置 iterations=3, reportable=True
sed -i "/^iterations[[:space:]]*=.*/c\iterations           = 3" "$CONFIG_NAME"
sed -i "/^reportable[[:space:]]*=.*/c\reportable           = True" "$CONFIG_NAME"

# ====== 自动采集硬件信息并写入cfg ======
# CPU型号
cpu_model=$(lscpu | grep 'Model name' | head -1 | awk -F: '{print $2}' | sed 's/^ *//')
sed -i "/^# *hw_cpu_name[[:space:]]*=.*/c\\   hw_cpu_name        = $cpu_model" "$CONFIG_NAME"
# CPU主频
cpu_mhz=$(lscpu | grep 'CPU MHz' | head -1 | awk -F: '{print $2}' | sed 's/^ *//')
sed -i "/^ *hw_cpu_nominal_mhz[[:space:]]*=.*/c\\   hw_cpu_nominal_mhz = $cpu_mhz" "$CONFIG_NAME"
# CPU最大主频
cpu_max_mhz=$(lscpu | grep 'CPU max MHz' | head -1 | awk -F: '{print $2}' | sed 's/^ *//')
sed -i "/^ *hw_cpu_max_mhz[[:space:]]*=.*/c\\   hw_cpu_max_mhz     = $cpu_max_mhz" "$CONFIG_NAME"
# CPU核心数
cpu_cores=$(lscpu | grep '^CPU(s):' | head -1 | awk -F: '{print $2}' | sed 's/^ *//')
sed -i "/^ *hw_ncores[[:space:]]*=.*/c\\   hw_ncores          = $cpu_cores" "$CONFIG_NAME"
# CPU颗数
cpu_sockets=$(lscpu | grep 'Socket(s):' | head -1 | awk -F: '{print $2}' | sed 's/^ *//')
sed -i "/^ *hw_nchips[[:space:]]*=.*/c\\   hw_nchips          = $cpu_sockets" "$CONFIG_NAME"
# 每核线程数
threads_per_core=$(lscpu | grep 'Thread(s) per core' | head -1 | awk -F: '{print $2}' | sed 's/^ *//')
sed -i "/^ *hw_nthreadspercore[[:space:]]*=.*/c\\   hw_nthreadspercore = $threads_per_core" "$CONFIG_NAME"
# 内存总容量
mem_total=$(grep MemTotal /proc/meminfo | awk '{print $2/1024 " MB"}')
sed -i "/^# *hw_memory_total[[:space:]]*=.*/c\\   hw_memory_total    = $mem_total" "$CONFIG_NAME"
# 内存类型和条数（需要root权限）
if [ "$(id -u)" -eq 0 ]; then
    mem_info=$(dmidecode -t memory 2>/dev/null | grep -iE 'Part Number|Size' | grep -v 'No Module Installed')
    mem_type=$(echo "$mem_info" | grep 'Part Number' | awk '{print $3}' | sort | uniq | paste -sd "," -)
    mem_num=$(echo "$mem_info" | grep -c 'Size:')
    sed -i "/^# *hw_memory_type[[:space:]]*=.*/c\\   hw_memory_type     = $mem_type" "$CONFIG_NAME"
    sed -i "/^# *hw_memory_num[[:space:]]*=.*/c\\   hw_memory_num      = $mem_num" "$CONFIG_NAME"
fi
# L1/L2/L3缓存
l1d=$(lscpu | grep 'L1d cache' | awk -F: '{print $2}' | sed 's/^ *//')
l1i=$(lscpu | grep 'L1i cache' | awk -F: '{print $2}' | sed 's/^ *//')
l2=$(lscpu | grep 'L2 cache' | awk -F: '{print $2}' | sed 's/^ *//')
l3=$(lscpu | grep 'L3 cache' | awk -F: '{print $2}' | sed 's/^ *//')
sed -i "/^ *hw_pcache[[:space:]]*=.*/c\\   hw_pcache          = L1d: $l1d, L1i: $l1i" "$CONFIG_NAME"
sed -i "/^ *hw_scache[[:space:]]*=.*/c\\   hw_scache          = L2: $l2" "$CONFIG_NAME"
sed -i "/^ *hw_tcache[[:space:]]*=.*/c\\   hw_tcache          = L3: $l3" "$CONFIG_NAME"
# 操作系统
if [ -f /etc/os-release ]; then
    os_name=$(grep -E '^PRETTY_NAME=' /etc/os-release | cut -d '=' -f2- | tr -d '"')
elif [ -f /etc/redhat-release ]; then
    os_name=$(cat /etc/redhat-release)
else
    os_name=$(uname -a)
fi
sed -i "/^ *sw_os[[:space:]]*=.*/c\\   sw_os              = $os_name" "$CONFIG_NAME"
# BIOS信息（需要root权限）
if [ "$(id -u)" -eq 0 ]; then
    bios_vendor=$(dmidecode -t bios 2>/dev/null | grep -m1 'Vendor:' | awk -F: '{print $2}' | sed 's/^ *//')
    bios_version=$(dmidecode -t bios 2>/dev/null | grep -m1 'Version:' | awk -F: '{print $2}' | sed 's/^ *//')
    bios_date=$(dmidecode -t bios 2>/dev/null | grep -m1 'Release Date:' | awk -F: '{print $2}' | sed 's/^ *//')
    sed -i "/^ *fw_bios[[:space:]]*=.*/c\\   fw_bios            = Vendor: $bios_vendor; Version: $bios_version; Release Date: $bios_date" "$CONFIG_NAME"
fi

# ================== 环境准备 ==================
cd "$SPECCPU_DIR"
source "$SPECCPU_DIR/shrc"

# ================== 获取CPU核心数并执行测试 ==================
cpu_cores=$(nproc)
log_info "检测到CPU核心数: $cpu_cores. 将动态配置 build_ncpus, copies, threads."

# 新增详细打印
log_info "即将开始SpecCPU2017测试"
echo -e "\033[36m[TEST INFO]\033[0m 测试类型: intrate, fprate, intspeed, fpspeed"
echo -e "\033[36m[TEST INFO]\033[0m 使用的config文件: $CONFIG_NAME"
echo -e "\033[36m[TEST INFO]\033[0m 平台: $platform ($arch)"
echo -e "\033[36m[TEST INFO]\033[0m CPU核心数: $cpu_cores"
echo -e "\033[36m[TEST INFO]\033[0m runcpu路径: $RUNCPU"

# 针对浮点类测试，动态设置 OMP_STACKSIZE 和 ulimit
if [[ "$1" == "fpspeed" || "$1" == "fprate" ]]; then
    export OMP_STACKSIZE=1G
    ulimit -s unlimited
    log_info "已为浮点测试设置 OMP_STACKSIZE=1G 和 ulimit -s unlimited"
fi

# 优先用绝对路径调用runspec/runcpu
if [ -x "$SPECCPU_DIR/bin/runcpu" ]; then
    RUNCPU="$SPECCPU_DIR/bin/runcpu"
else
    RUNCPU="runspec"
fi

# 用法: bash Speccpu_2017.sh [test|ref]
MODE=${1:-ref}
if [[ "$MODE" == "test" ]]; then
    SIZE="test"
    ITER="--iterations=1"
else
    SIZE="ref"
    ITER=""
fi

log_info "开始执行SpecCPU2017测试 (intrate, fprate, intspeed, fpspeed)..."
if ! $RUNCPU --config=$CONFIG_NAME \
             --define build_ncpus=$cpu_cores \
             --define copies=$cpu_cores \
             --define threads=$cpu_cores \
             --action=run \
             --size=$SIZE \
             --tune=base \
             $ITER \
             all \
             | tee "$OUTPUT_DIR/speccpu_run.log"; then
    log_error "SpecCPU2017测试执行失败"
    exit 1
fi

# 等待所有分数文件都生成
for f in result/CPU2017.001.fprate.txt result/CPU2017.001.fpspeed.txt result/CPU2017.001.intrate.txt result/CPU2017.001.intspeed.txt; do
  i=0
  while [ ! -f "$f" ] && [ $i -lt 60 ]; do
    sleep 2
    i=$((i+1))
  done
done

# 再 sleep 2 秒，确保文件写盘
sleep 2

# ================== 拷贝结果 ==================
log_info "拷贝测试结果到$OUTPUT_DIR..."
cp -r "$SPECCPU_DIR/result" "$OUTPUT_DIR/"
cp "$SPECCPU_DIR/config/$CONFIG_NAME" "$OUTPUT_DIR/"
cp "$LOG_FILE" "$OUTPUT_DIR/"

# ================== 简要报告 ==================
REPORT_MD="$OUTPUT_DIR/speccpu_report.md"
# 采集硬件信息
CPU_MODEL=$(lscpu | grep 'Model name' | head -1 | awk -F: '{print $2}' | sed 's/^ *//')
CPU_NUM=$(lscpu | grep '^CPU(s):' | head -1 | awk -F: '{print $2}' | sed 's/^ *//')
MEM_INFO=$(dmidecode -t memory 2>/dev/null | grep -iE 'Part Number|Size' | grep -v 'No Module Installed')
MEM_TYPE=$(echo "$MEM_INFO" | grep 'Part Number' | awk '{print $3}' | sort | uniq | paste -sd "," -)
MEM_NUM=$(echo "$MEM_INFO" | grep -c 'Size:')
# 统一抓取SPEC四项结果
int_rate=$(grep "SPECrate(R)2017_int_base"   $RESULT_DIR/*.txt 2>/dev/null | head -1 | awk '{print $NF}')
int_speed=$(grep "SPECspeed(R)2017_int_base" $RESULT_DIR/*.txt 2>/dev/null | head -1 | awk '{print $NF}')
fp_rate=$(grep "SPECrate(R)2017_fp_base"     $RESULT_DIR/*.txt 2>/dev/null | head -1 | awk '{print $NF}')
fp_speed=$(grep "SPECspeed(R)2017_fp_base"   $RESULT_DIR/*.txt 2>/dev/null | head -1 | awk '{print $NF}')
echo "[DEBUG] int_rate=$int_rate, int_speed=$int_speed, fp_rate=$fp_rate, fp_speed=$fp_speed"
{
    echo "# SpecCPU2017 测试报告"
    echo "- 平台: $platform ($arch)"
    echo "- CPU型号: $CPU_MODEL"
    echo "- CPU数量: $CPU_NUM"
    echo "- 内存型号: $MEM_TYPE"
    echo "- 内存条数: $MEM_NUM"
    echo "- config文件: $CONFIG_NAME"
    echo "- 测试时间: $(date '+%Y-%m-%d %H:%M:%S')"
    echo "- 详细日志见 speccpu_run.log 和 speccpu_install.log"
    echo ""
    echo "## 关键结果摘要"
    if [ -d "$OUTPUT_DIR/result" ]; then
        echo "SPECrate(R)2017_int_base: $int_rate"
        echo "SPECspeed(R)2017_int_base: $int_speed"
        echo "SPECrate(R)2017_fp_base: $fp_rate"
        echo "SPECspeed(R)2017_fp_base: $fp_speed"
    else
        echo "未找到详细结果文件。"
    fi
} > "$REPORT_MD"
log_info "SpecCPU2017测试完成，所有结果已保存到$OUTPUT_DIR，报告见$REPORT_MD"

# 复制md报告到最终输出目录
cp "$REPORT_MD" "$FINAL_OUTDIR/"

# ========== 新增：提取SPEC结果到最终目录 ===========
> "$FINAL_OUTFILE"
[[ -n "$int_rate" ]]  && echo "SPECrate(R)2017_int_base: $int_rate"   >> "$FINAL_OUTFILE"
[[ -n "$int_speed" ]] && echo "SPECspeed(R)2017_int_base: $int_speed" >> "$FINAL_OUTFILE"
[[ -n "$fp_rate" ]]   && echo "SPECrate(R)2017_fp_base: $fp_rate"     >> "$FINAL_OUTFILE"
[[ -n "$fp_speed" ]]  && echo "SPECspeed(R)2017_fp_base: $fp_speed"   >> "$FINAL_OUTFILE"
log_info "SPEC结果已输出到 $FINAL_OUTFILE"
