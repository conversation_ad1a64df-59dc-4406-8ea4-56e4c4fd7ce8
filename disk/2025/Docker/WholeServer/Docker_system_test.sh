#!/bin/bash

# =====================
# 整机自动化测试主控脚本
# =====================
# 说明：
# 1. 除reboot测试外，所有测试项均以docker容器方式运行
# 2. reboot测试在主机下运行
# 3. 自动识别平台（Intel/AMD/ARM），部分测试项自动分支
# 4. 自动收集结果并生成报告
# 5. 关键步骤均有中文注释

# ====== 配置区 ======
DOCKER_IMAGE="centos7-system-test:v0.9"   # x86/Intel/AMD镜像
DOCKER_IMAGE_URL="http://************/huzz/DOCKER/${DOCKER_IMAGE}.tar"
DOCKER_IMAGE_ARM="centos7-sytem-test-arm:v0.1"   # ARM镜像  
DOCKER_IMAGE_URL_ARM="http://************/huzz/DOCKER/${DOCKER_IMAGE_ARM}.tar"
OUTPUT_DIR="/opt/output"            # 主机结果目录
REPORT_MD="${OUTPUT_DIR}/final_report.md"
LOG_DIR="${OUTPUT_DIR}/logs"
REBOOT_SCRIPT="/home/<USER>/reboot_new1.sh"      # 请替换为实际reboot脚本路径

# ====== 镜像检查与下载 ======
check_and_load_image() {
    # 根据平台选择镜像和URL
    local image_name image_tag tar_path image_url
    if [ "$platform" = "arm" ]; then
        image_name="${DOCKER_IMAGE_ARM%%:*}"
        image_tag="${DOCKER_IMAGE_ARM##*:}"
        tar_path="/tmp/${image_name}.tar"
        image_url="$DOCKER_IMAGE_URL_ARM"
        image_full="$DOCKER_IMAGE_ARM"
    else
        image_name="${DOCKER_IMAGE%%:*}"
        image_tag="${DOCKER_IMAGE##*:}"
        tar_path="/tmp/${image_name}.tar"
        image_url="$DOCKER_IMAGE_URL"
        image_full="$DOCKER_IMAGE"
    fi
    # 判断本地是否有指定镜像和标签
    if ! docker images | grep -qE "^${image_name}[[:space:]]+${image_tag}[[:space:]]"; then
        echo "本地未找到镜像 ${image_full}，准备下载并加载..."
        # 下载镜像tar包
        if [ ! -f "$tar_path" ]; then
            wget -O "$tar_path" "$image_url" || { echo "镜像下载失败"; exit 1; }
        fi
        # 加载镜像
        docker load < "$tar_path" || { echo "镜像加载失败"; exit 1; }
        # 检查标签是否正确，否则自动打标签
        if ! docker images | grep -qE "^${image_name}[[:space:]]+${image_tag}[[:space:]]"; then
            IMAGE_ID=$(docker images --format '{{.Repository}}:{{.Tag}} {{.ID}}' | grep "${image_name}" | awk '{print $2}' | head -1)
            if [ -n "$IMAGE_ID" ]; then
                docker tag "$IMAGE_ID" "${image_name}:${image_tag}"
            fi
        fi
        echo "镜像 ${image_full} 已加载"
    else
        echo "本地已存在镜像 ${image_full}"
    fi
}

# ====== 平台识别 ======
get_platform() {
    cpu_vendor=$(lscpu | grep 'Vendor ID' | awk '{print $3}')
    cpu_arch=$(uname -m)
    if echo "$cpu_vendor" | grep -qi "intel"; then
        platform="intel"
    elif echo "$cpu_vendor" | grep -qi "amd"; then
        platform="amd"
    elif echo "$cpu_arch" | grep -qi "aarch64"; then
        platform="arm"
    else
        platform="unknown"
    fi
    echo "检测到平台: $platform"
}

# ====== 环境准备 ======
init_env() {
    mkdir -p "$OUTPUT_DIR" "$LOG_DIR"
    # 检查docker
    if ! command -v docker &> /dev/null; then
        echo "未检测到Docker，正在自动安装..."
        yum install -y docker || { echo "Docker安装失败，请手动检查！"; exit 1; }
    fi
    # 检查docker服务
    if ! systemctl is-active docker &> /dev/null; then
        echo "Docker服务未运行，正在启动..."
        systemctl start docker || { echo "Docker启动失败"; exit 1; }
    fi
    echo "环境初始化完成"
}

# ====== 通用docker测试函数 ======
run_in_docker() {
    local script_name="$1"
    local test_name="$2"
    local extra_args="$3"
    local result_dir="$4"
    local container_result_dir="$5"
    # 根据平台选择镜像
    local image_full
    if [ "$platform" = "arm" ]; then
        image_full="$DOCKER_IMAGE_ARM"
    else
        image_full="$DOCKER_IMAGE"
    fi
    echo -e "\n===== 开始 $test_name 测试 ====="
    docker run --rm --privileged --net=host \
        -v "$OUTPUT_DIR/$result_dir:$container_result_dir" \
        -e PLATFORM="$platform" \
        $image_full \
        bash -c "/home/<USER>/$script_name $extra_args"
    if [ $? -eq 0 ]; then
        echo "$test_name 测试完成"
    else
        echo "$test_name 测试失败"
        # 不 exit 1，继续后续测试
    fi
}

# ====== 各项测试 ======
run_speccpu2017() {
    run_in_docker "Speccpu_2017.sh" "SpecCPU2017" "" "speccpu2017" "/home/<USER>/SpecCPU2017"
}

run_specjbb() {
    run_in_docker "SpecJBB.sh" "SpecJBB" "" "specjbb" "/home/<USER>/SpecJBB"
}

run_sysbench() {
    run_in_docker "SysbenchTest.sh" "sysbench" "" "sysbench" "/home/<USER>/sysbench"
}

run_stressng() {
    run_in_docker "stressng.sh" "stress-ng" "" "stressng" "/home/<USER>/stressng"
}

run_linpack() {
    run_in_docker "linpack.sh" "Linpack" "" "linpack" "/home/<USER>/linpack"
}

run_stream() {
    run_in_docker "StreamTest.sh" "Stream" "" "stream" "/home/<USER>/stream"
}

run_memtester() {
    run_in_docker "memtester.sh" "memtester" "" "memtester" "/home/<USER>/memtester"
}

run_mlc() {
    if [ "$platform" = "arm" ]; then
        echo "ARM平台不支持MLC，跳过"
        return
    fi
    run_in_docker "MLC_test_for_Intel_and_AMD_LongTerm.sh" "MLC" "" "mlc" "/home/<USER>/mlc"
}

run_iperf3() {
    run_in_docker "iperf3.sh" "iperf3" "" "iperf3" "/home/<USER>/iperf3"
}

run_netperf() {
    run_in_docker "netperf.sh" "netperf" "" "netperf" "/home/<USER>/netperf"
}

run_pktgen() {
    run_in_docker "pktgen.sh" "pktgen" "" "pktgen" "/home/<USER>/pktgen"
}

run_fio() {
    run_in_docker "fio.sh" "fio" "" "fio" "/home/<USER>/fio"
}

run_power() {
    run_in_docker "AutoPowerTest.sh" "整机功耗" "" "power" "/home/<USER>/power"
}

# ====== 多盘测试 ======
run_multi_drive_test() {
    local nvme_count hdd_count
    nvme_count=$(lsblk -d -n -o NAME,TYPE | grep -E '^nvme[0-9]+' | wc -l)
    hdd_count=$(lsblk -d -n -o NAME,TYPE | grep -E 'sd[a-z]' | wc -l)
    local test_types=()
    if [ "$nvme_count" -gt 0 ]; then
        test_types+=("nvme")
    fi
    if [ "$hdd_count" -gt 0 ]; then
        test_types+=("hdd")
    fi
    if [ ${#test_types[@]} -eq 0 ]; then
        echo "未检测到NVMe SSD或HDD，跳过多盘测试"
        return
    fi
    for t in "${test_types[@]}"; do
        if [ "$t" = "nvme" ]; then
            run_in_docker "multiDriveTest.sh" "多盘NVMe测试" "nvme" "multiDriveTest/nvme" "/home/<USER>/multiDriveTest/nvme"
        elif [ "$t" = "hdd" ]; then
            run_in_docker "multiDriveTest.sh" "多盘HDD测试" "hdd" "multiDriveTest/hdd" "/home/<USER>/multiDriveTest/hdd"
        fi
    done
}

# BC计算π值测试（主机下运行）
run_bc_pi() {
    echo -e "\n===== 开始BC计算π值测试（主机下运行） ====="
    
    # 创建BC测试结果目录
    local bc_dir="$OUTPUT_DIR/bc_pi"
    mkdir -p "$bc_dir"
    
    # 检查bc是否安装
    if ! command -v bc &> /dev/null; then
        echo "bc命令未安装，正在安装..."
        yum install -y bc || { echo "bc安装失败"; exit 1; }
    fi
    
    local result_file="$bc_dir/bc_pi_result.txt"
    local temp_file="$bc_dir/bc_pi_temp.txt"
    
    echo "开始执行BC计算π值测试（scale=10000）..."
    echo "测试命令: time echo \"scale=10000; 4*a(1)\" | bc -l" > "$result_file"
    echo "测试开始时间: $(date '+%Y-%m-%d %H:%M:%S')" >> "$result_file"
    echo "========================================" >> "$result_file"
    
    # 执行测试并捕获time命令的输出
    { time echo "scale=10000; 4*a(1)" | bc -l > "$temp_file"; } 2>> "$result_file"
    
    echo "========================================" >> "$result_file"
    echo "测试结束时间: $(date '+%Y-%m-%d %H:%M:%S')" >> "$result_file"
    
    # 检查计算结果（只显示前100位和后10位）
    if [ -f "$temp_file" ] && [ -s "$temp_file" ]; then
        echo "" >> "$result_file"
        echo "计算结果验证（π值前100位）:" >> "$result_file"
        head -c 100 "$temp_file" >> "$result_file"
        echo "..." >> "$result_file"
        echo "" >> "$result_file"
        echo "计算结果验证（π值后10位）:" >> "$result_file"
        tail -c 10 "$temp_file" >> "$result_file"
        echo "" >> "$result_file"
        
        # 检查结果是否以3.14开头
        if head -c 4 "$temp_file" | grep -q "3.14"; then
            echo "结果验证: 通过（π值计算正确）" >> "$result_file"
        else
            echo "结果验证: 失败（π值计算可能有误）" >> "$result_file"
        fi
        
        # 计算结果的总位数
        local total_digits=$(wc -c < "$temp_file")
        echo "计算精度: ${total_digits}位小数" >> "$result_file"
    else
        echo "错误: 计算结果文件为空或不存在" >> "$result_file"
    fi
    
    # 清理临时文件
    rm -f "$temp_file"
    
    if [ $? -eq 0 ]; then
        echo "BC计算π值测试完成，结果保存到: $result_file"
    else
        echo "BC计算π值测试失败"; exit 1
    fi
}

# reboot测试在主机下运行
run_reboot() {
    echo -e "\n===== 开始reboot测试（主机下运行） ====="
    bash "$REBOOT_SCRIPT" 100  # 100次为例，可根据需要调整
    if [ $? -eq 0 ]; then
        echo "reboot测试完成"
    else
        echo "reboot测试失败"; exit 1
    fi
}

# ====== 汇总报告 ======
generate_report() {
    echo "# 整机自动化测试报告" > "$REPORT_MD"
    echo -e "\n测试时间: $(date '+%Y-%m-%d %H:%M:%S')" >> "$REPORT_MD"

    echo -e "\n## 平台信息" >> "$REPORT_MD"
    echo '```' >> "$REPORT_MD"
    lscpu >> "$REPORT_MD"
    echo '```' >> "$REPORT_MD"

    echo -e "\n## 各项测试结果" >> "$REPORT_MD"

    # 定义测试项和它们的主要结果文件
    # 格式为 "测试目录名:测试报告标题:主要结果文件名"
    local test_items=(
        "speccpu2017:SpecCPU2017:speccpu_report.md"
        "bc_pi:BC计算π值:bc_pi_result.txt"
        "specjbb:SpecJBB:specjbb_report.md"
        "sysbench:sysbench:sysbench_report.txt"
        "stressng:stress-ng:stressng_report.txt"
        "linpack:Linpack:linpack_report.md"
        "stream:Stream:stream_result.txt"
        "memtester:memtester:memtester.log"
        "mlc:MLC:mlc_report.txt"
        "iperf3:iperf3:iperf3_report.txt"
        "netperf:netperf:netperf_report.txt"
        "pktgen:pktgen:pktgen_report.txt"
        "fio:fio:fio_summary.txt"
        "multiDriveTest/nvme:多盘NVMe测试:multiDriveTest_report.md"
        "multiDriveTest/hdd:多盘HDD测试:multiDriveTest_report.md"
        "power:整机功耗:power_report.md"
        "reboot:Reboot:reboot.log"
    )

    for item in "${test_items[@]}"; do
        local dir_name=$(echo "$item" | cut -d':' -f1)
        local test_title=$(echo "$item" | cut -d':' -f2)
        local report_file=$(echo "$item" | cut -d':' -f3)
        local result_path="$OUTPUT_DIR/$dir_name/$report_file"
        local image_dir="$OUTPUT_DIR/images/$dir_name"

        echo -e "\n### $test_title" >> "$REPORT_MD"

        # 插入主要结果文件内容
        if [ -f "$result_path" ]; then
            cat "$result_path" >> "$REPORT_MD"
        fi

        # 插入图片
        if [ -d "$image_dir" ]; then
            for img in "$image_dir"/*.{png,jpg,jpeg,svg}; do
                [ -e "$img" ] || continue
                img_name=$(basename "$img")
                echo -e "\n![$img_name](images/$dir_name/$img_name)" >> "$REPORT_MD"
            done
        fi
    done

    echo -e "\n报告生成完毕：$REPORT_MD"
}

# ====== 主流程 ======
main() {
    init_env
    check_and_load_image
    get_platform
    run_bc_pi          # 添加BC计算π值测试
    run_speccpu2017
    run_specjbb
    run_sysbench
    #run_stressng
    run_linpack
    run_stream
    #run_memtester
    #run_mlc
    #run_iperf3
    #run_netperf
    #run_pktgen
    ##run_fio
    run_multi_drive_test
    run_power
    generate_report
    #run_reboot
    echo -e "\n所有测试完成，报告已生成。"
}

main "$@"