#!/bin/bash
export LANG=en_US.UTF-8
# 增加GPU的压力测试
#整体先做CPU压力测试，再做GPU压力测试，GPU不分档，只测总体压力
#set -e  # 遇到错误时立即退出脚本
# 配置参数
RESULT_LOCATION="/home/<USER>/power_test_result"
STRESS_DURATION=80 #默认值为80
LOAD_LEVELS=(0 10 20 30 40 50 60 70 80 90 98)
LOG_FILE="${RESULT_LOCATION}/script.log"
CURL_TIMEOUT=30
ROOT_DIR=${RESULT_LOCATION}

show_help() {
    cat << EOF
使用方法: $(basename "$0") [时间]

功能描述：
    本脚本用于服务器离线功耗压力测试，包含以下测试项目：
    - CPU 压力测试（负载等级：0-98%）
    - 内存压力测试（负载等级：0-95%）
    - 存储设备压力测试
    - GPU 压力测试（如果检测到支持的 GPU）
    - 系统功耗监控
    - 自动生成功耗曲线图

参数：
    [时间]     设置每个档位的测试时间（秒），默认为80秒
    -h, --help 显示此帮助信息
    
环境要求：
    - 需要 root 权限
    - 需要安装：stress-ng, ipmitool, fio, gnuplot, dmidecode, smartctl
    
输出结果：
    - 系统信息报告
    - 功耗数据曲线图
    - 详细测试报告
    - 测试数据压缩包

作者：MT-Huzezhi
版本：V1.0
EOF
    exit 0
}

# 处理命令行参数
if [ $# -eq 1 ]; then
    if [[ "$1" == "-h" || "$1" == "--help" ]]; then
        show_help
    elif [[ "$1" =~ ^[0-9]+$ ]]; then
        STRESS_DURATION=$1
    else
        echo "错误: 参数必须是一个有效的整数值"
        show_help
    fi
elif [ $# -gt 1 ]; then
    echo "错误: 参数过多"
    show_help
fi

# 验证 STRESS_DURATION 是否为有效数字
if [[ -n "${STRESS_DURATION}" ]] && ! [[ "${STRESS_DURATION}" =~ ^[0-9]+$ ]]; then
    echo "错误: STRESS_DURATION 必须是一个有效的整数"
    exit 1
fi

# 日志函数
log() {
    local message="$(date '+%Y-%m-%d %H:%M:%S') - $1"
    echo "${message}"
    
    # Create the directory if it doesn't exist
    mkdir -p "$(dirname "${LOG_FILE}")"
    
    if ! echo "${message}" >> "${LOG_FILE}" 2>/dev/null; then
        echo "警告：无法写入日志文件 ${LOG_FILE}"
    fi
}

# 记录测试时间设置
log "stress duration: ${STRESS_DURATION} seconds"
handle_error() {
    local code=$1
    local msg=$2
    echo "错误[$code]: $msg"
    exit $code
}
# 初始化函数
init() {
    log "开始初始化..."
    mkdir -p "${RESULT_LOCATION}" || handle_error 2 "无法创建结果目录"

    # 检测架构
    local arch=$(uname -m)
    
    # 正确提取内核版本的主版本号和次版本号
    local kernel_major=$(uname -r | awk -F. '{print $1}')
    local kernel_minor=$(uname -r | awk -F. '{print $2}')

    # 首先根据架构选择不同的处理方式
    if [[ "$arch" == "aarch64" ]]; then
        echo "[INFO] 检测到 ARM 架构，使用本地mtos-arm.repo" | tee -a "$LOG_FILE"
        rm -rf /etc/yum.repos.d/*
        curl -o /etc/yum.repos.d/mtos-arm.repo http://************/huzz/DOCKER/tools/mtos-arm.repo
        yum clean all 2>>"$LOG_FILE" || true
        yum makecache fast 2>>"$LOG_FILE" || yum makecache 2>>"$LOG_FILE" || true
    else
        # x86架构，根据内核版本选择不同的源
        if [[ $kernel_major -ge 5 ]]; then
            echo "[INFO] 检测到x86架构，内核版本 >= 5.0，使用CentOS-Base.repo" | tee -a "$LOG_FILE"
            rm -rf /etc/yum.repos.d/*
            curl -o /etc/yum.repos.d/CentOS-Base.repo http://************/huzz/DOCKER/tools/CentOS-Base.repo
            yum clean all 2>>"$LOG_FILE" || true
            yum makecache fast 2>>"$LOG_FILE" || yum makecache 2>>"$LOG_FILE" || true
            yum install -y epel-release 2>>"$LOG_FILE" || true
        else
            echo "[INFO] 检测到x86架构，内核版本 < 5.0，使用mtos源" | tee -a "$LOG_FILE"
            rm -rf /etc/yum.repos.d/*
            curl -o /etc/yum.repos.d/mtos.repo http://************/huzz/DOCKER/tools/mtos.repo
            yum clean all 2>>"$LOG_FILE" || true
            yum makecache fast 2>>"$LOG_FILE" || yum makecache 2>>"$LOG_FILE" || true
        fi
    fi

    # 定义工具和对应的包名 - 移除zip和unzip
    local tools=("stress-ng" "ipmitool" "fio" "gnuplot" "dmidecode" "smartctl" "iperf3" "cpupower") 
    local packages=("stress-ng" "ipmitool" "fio" "gnuplot" "dmidecode" "smartmontools" "iperf3" "kernel-tools")

    
    # 使用索引遍历两个数组，确保一一对应
    for i in "${!tools[@]}"; do
        local tool="${tools[$i]}"
        local package="${packages[$i]}"
        
        if ! command -v "${tool}" &>/dev/null; then
            log "${tool} 未安装，正在安装 ${package}..."
            if ! yum install -y "${package}"; then
                log "警告: 无法通过 yum 安装 ${package}，尝试其他安装方法..."
                case "${tool}" in
                    "stress-ng")
                        yum install -y epel-release && yum install -y stress-ng
                        ;;
                    "smartctl")
                        yum install -y smartmontools
                        ;;
                    "iperf3")
                        yum install -y epel-release && yum install -y iperf3
                        ;;
                    "cpupower")
                        log "尝试安装 kernel-tools 包以获取 cpupower 工具"
                        yum install -y kernel-tools
                        ;;
                    *)
                        if [ "${tool}" = "iperf3" ]; then
                            log "警告: 无法安装 iperf3，网卡压测可能无法执行"
                        else
                            handle_error 3 "无法安装 ${package}"
                        fi
                        ;;
                esac
            fi
            
            if command -v "${tool}" &>/dev/null; then
                log "${tool} 安装成功"
            else
                if [ "${tool}" = "iperf3" ]; then
                    log "警告: iperf3 安装失败，网卡压测可能无法执行"
                elif [ "${tool}" = "cpupower" ]; then
                    log "警告: cpupower 安装失败，频率监控功能可能不可用"
                else
                    handle_error 3 "安装 ${tool} 失败"
                fi
            fi
        else
            log "${tool} 已安装"
        fi
    done

    modprobe ipmi_devintf || log "警告: 无法加载 ipmi_devintf 模块"
    modprobe ipmi_si || log "警告: 无法加载 ipmi_si 模块"
    log "初始化完成"
}

# 添加绘图函数
generate_power_graph() {
    echo "生成功耗曲线图..."
    local input_file="${RESULT_LOCATION}/System_Power.csv"
    local output_file="${RESULT_LOCATION}/power_curve.png"
    local temp_file="${RESULT_LOCATION}/temp_power.txt"

    # 获取最大负载等级
    local max_load=$(awk -F',' 'NR>1 {if($1>max) max=$1} END {print max}' "${input_file}")
    # 处理每档功耗：idle档（0%）取最小值，其他档取最大值
    awk -F',' 'NR>1 {
        values[$1][count[$1]++] = $2
    }
    END {
        for (load in values) {
            n = count[load]
            if (load == 0) {
                # idle档取最小值
                min_val = values[load][0]
                for (i = 1; i < n; i++) {
                    if (values[load][i] < min_val) min_val = values[load][i]
                }
                printf "%s %.0f\n", load, min_val
            } else {
                # 其他档取最大值
                max_val = values[load][0]
                for (i = 1; i < n; i++) {
                    if (values[load][i] > max_val) max_val = values[load][i]
                }
                printf "%s %.0f\n", load, max_val
            }
        }
    }' "${input_file}" | sort -n > "${temp_file}"

    # 添加一个额外的点来确保最后一个台阶有足够长的横线
    last_load=$(tail -n 1 "${temp_file}" | awk '{print $1}')
    last_power=$(tail -n 1 "${temp_file}" | awk '{print $2}')
    echo "$((last_load + 5)) $last_power" >> "${temp_file}"

    # 检查临时文件是否为空
    if [ ! -s "${temp_file}" ]; then
        echo "错误：无法生成有效的功耗数据"
        return 1
    fi
    echo "Debug: last_load = $last_load"
    echo "Debug: temp_file content:"
    cat "$temp_file"

    # 绘图命令保持不变
    gnuplot <<EOF
set terminal png size 800,600
set output "$output_file"
set title "CPU Utilization vs Power Consumption"
set xlabel "CPU Utilization (%)"
set ylabel "Power (W)"
set grid
set datafile separator " "
set key at graph 0.5, 0.98 # 将图例向右移动
set style data steps
set xtics 10
set ytics 50
set xrange [-5:110]
set yrange [*:*]  # 自动调整Y轴范围
set boxwidth 0.8  # 设置台阶宽度
set style fill solid 0.5  # 添加填充效果

# 设置标签样式
set label font ",10"
set label textcolor rgb "#000000"

# 从临时文件读取数据并添加标签
plot "$temp_file" using 1:2 with steps lw 2 lt rgb "#FF0000" title "Power Curve", \
     "$temp_file" using 1:2:(\$1 < $last_load ? sprintf("%.0f", \$2) : "") with labels offset char 2,0.5 notitle, \
     "$temp_file" using (\$1==$last_load ? \$1+2 : 1/0):2:(sprintf("%.0f", \$2)) with labels offset char 2,0.5 notitle
EOF

    # 检查是否成功生成图片
    if [ -f "$output_file" ]; then
        echo "功耗曲线图已生成: $output_file"
    else
        echo "功耗曲线图生成失败"
    fi

    # 清理临时文件
    rm -f "${temp_file}"
}


# 获取GPU类型，没检测到已知GPU类型时返回1，默认使用CPU
detect_gpu_type() {
    if nvidia-smi >/dev/null 2>&1; then
        GPU_TYPE="NVIDIA"
        return 0
    fi
    if npu-smi info >/dev/null 2>&1; then # 假设这是华为昇腾的命令
        GPU_TYPE="Ascend"
        return 0
    fi
    if cnmon >/dev/null 2>&1; then # 假设这是寒武纪的命令
        GPU_TYPE="Cambricon"
        return 0
    fi
    if dlsmi >/dev/null 2>&1; then # 假设这是燧原的命令 (Goldwasser)
        GPU_TYPE="Goldwasser"
        return 0
    fi
    return 1
}


# 系统信息收集函数
system_info() {
    log "收集系统信息..."
    {
        echo "服务器品牌: $(dmidecode -t 1 | grep "Manufacturer" | awk '{print $2}')"
        echo "型号: $(dmidecode -t 1 | grep "Product Name" | awk '{print $3}')"
        echo "机器SN: $(dmidecode -t 1 | grep "Serial Number" | awk '{print $3}')"
        echo "BMC版本: $(dmidecode -t bios | grep "Version" | awk '{print $2}')"
        echo "BIOS版本: $(ipmitool mc info | grep "Firmware Revision" | awk '{print $4}')"
        echo "内核版本: $(uname -r)"
        echo "CPU: $(lscpu | grep -E "型号名称|Model name" | awk '{print $5 $6 $7 $8}')"
        echo "内存: $(dmidecode -t memory | grep "Part Number" | grep -v 'NO DIMM')"
        echo "单条内存容量: $(dmidecode -t memory | grep "Size" | head -n 1 | awk '{print $2$3}')"
        echo "PMM:"
        echo "GPU list:"
        lspci | grep -i VGA
        echo "HDD/SSD list:"
        lsblk
        echo "整机功耗:"
        ipmitool sdr | grep -E 'Power  |Total_Power|Pwr Consumption'
        echo "风扇转速:"
        ipmitool sdr | grep "FAN"
    } >"${RESULT_LOCATION}/SystemInfo.log"
    log "系统信息收集完成"
}

get_power_reading() {
    local mode=$1  # 新增参数，min表示取最小值，其它取最大值
    local power=0
    local IPMI_TIMEOUT=25

    log "开始获取功耗读数" >&2

    local power_values=($(timeout $IPMI_TIMEOUT ipmitool sdr | grep -Ei '^Power  |^Total_Power|^Pwr Consumption|^POWER_WATTS  |Sys_Total_Power|Power1|Power2' | awk '{print $(NF-3)}' | grep -E '^[0-9]+([.][0-9]+)?$'))

    if [ ${#power_values[@]} -gt 0 ]; then
        if [ "$mode" = "min" ]; then
            # 取最小值
            local min_power=${power_values[0]}
            for value in "${power_values[@]}"; do
                if (( $(echo "$value < $min_power" | bc -l) )); then
                    min_power=$value
                fi
            done
            power=$min_power
            log "检测到多个功耗值: ${power_values[*]}, 使用最小值: ${power}W" >&2
        else
            # 取最大值
            local max_power=0
            for value in "${power_values[@]}"; do
                if (( $(echo "$value > $max_power" | bc -l) )); then
                    max_power=$value
                fi
            done
            power=$max_power
            log "检测到多个功耗值: ${power_values[*]}, 使用最大值: ${power}W" >&2
        fi
    fi

    # 如果上面的方法没有得到有效值，尝试使用 dcmi 命令
    if [ "$power" = "0" ]; then
        log "尝试使用 dcmi 命令获取功耗读数" >&2
        power=$(timeout $IPMI_TIMEOUT ipmitool dcmi power reading | grep 'Average power reading over sample period' | awk '{print $7}')
    fi

    if [ -z "$power" ] || ! [[ "$power" =~ ^[0-9]+([.][0-9]+)?$ ]]; then
        log "警告: 无法获取有效的功耗读数，使用0作为默认值" >&2
        power=0
    fi

    log "最终获取到的功耗读数: ${power}W" >&2
    echo "${power}"
}

# 添加网卡压测清理函数
cleanup_network_test() {
    log "清理网卡压力测试进程..."

    # 终止所有iperf3进程
    if pgrep iperf3 >/dev/null; then
        killall iperf3 2>/dev/null
        log "已终止所有iperf3进程"
    sleep 1
        if pgrep iperf3 >/dev/null; then
            killall -9 iperf3 2>/dev/null
            log "已强制终止所有iperf3进程"
        fi
    fi

    log "网卡压力测试清理完成"
}

network_stress_test() {
    local duration=$1
    cleanup_network_test
    log "开始执行网卡压力测试..."

    # 检查是否安装了iperf3
    if ! command -v iperf3 &>/dev/null; then
        log "iperf3未安装，正在安装..."
        yum install -y epel-release &>/dev/null
        yum install -y iperf3 &>/dev/null || {
            log "警告: 无法安装iperf3，网卡压测将被跳过"
            return 1
        }
    fi

    # 获取所有网卡接口（排除lo和虚拟接口）
    local interfaces=($(ip -o link show | grep -v "lo\|vir\|docker\|br-\|veth" | awk -F': ' '{print $2}'))

    if [ ${#interfaces[@]} -eq 0 ]; then
        log "未检测到物理网卡接口，网卡压测将被跳过"
        return 1
    fi

    log "检测到以下网卡接口: ${interfaces[*]}"

    # 为每个网卡接口启动iperf3测试
    for iface in "${interfaces[@]}"; do
        local ip_addr=$(ip -o -4 addr show dev "$iface" | awk '{print $4}' | cut -d'/' -f1 | head -1)

        if [ -z "$ip_addr" ]; then
            log "接口 $iface 没有IPv4地址，跳过"
            continue
        fi

        log "在接口 $iface ($ip_addr) 上启动网络压力测试"

        # 启动TCP服务器
        iperf3 -s -B "$ip_addr" -D -p 5201

        # 启动UDP服务器 - 修复：移除UDP选项，服务器端不需要指定UDP
        iperf3 -s -B "$ip_addr" -D -p 5202

        # 启动TCP测试
        iperf3 -c "$ip_addr" -t "$duration" -P 16 -b 0 > /dev/null 2>&1 &

        # 启动UDP测试 - 添加UDP选项到客户端
        iperf3 -c "$ip_addr" -u -t "$duration" -P 16 -b 10G -p 5202 > /dev/null 2>&1 &
    done

    log "网卡压力测试已启动，将持续 $duration 秒"
    return 0
}


# 采样idle状态功耗
sample_idle_power() {
    local sample_interval=10
    log "采样idle状态功耗，持续${STRESS_DURATION}秒..."

    local end=$((SECONDS + STRESS_DURATION))
    while [ $SECONDS -lt $end ]; do
        local power
        power=$(get_power_reading min)

        echo "0,${power},0,0" >>"${RESULT_LOCATION}/System_Power.csv"
        sleep "${sample_interval}"
    done
}

# 收集CPU频率
collect_freq() {
    local cpu_load=$1
    local timeout=10  # 设置超时时间，单位为秒

    log "开始收集 ${cpu_load}% 负载的频率数据"
    timeout $timeout cpupower monitor -i 1 | awk -v cpu_load="${cpu_load}" 'NR > 2 {if ($NF ~ /^[0-9]+([.][0-9]+)?$/) print cpu_load "," $NF "," NR-2;}' >>"${RESULT_LOCATION}/Fre_suma.csv"
    log "频率数据收集完成"
}

function GPU_Stress_Test() {
    # shellcheck disable=SC2164
    cd $ROOT_DIR  || exit
    rm -rf stress
    rm -f gpu-stress-test.tar
    wget "https://saturn.sankuai.com/saturn/testTool?toolName=gpu-stress-test.tar" -O gpu-stress-test.tar
    tar xvf gpu-stress-test.tar
    cd stress || exit
    # Call GPU script
    if [ "$GPU_TYPE" = "NVIDIA" ]; then
        sh gpu-burn-test.sh -a "$1,0,0,0,$2,1" >/dev/null &
    elif [ "$GPU_TYPE" = "Ascend" ]; then
        sh ascend-stress-test.sh -a "$1,0,0,0,$2" >/dev/null &
    elif [ "$GPU_TYPE" = "Cambricon" ]; then
        sh cambricon-stress-test.sh -a "$1,0,0,0,$2" >/dev/null &
    elif [ "$GPU_TYPE" = "Goldwasser" ]; then
        sh goldwasser-stress-test.sh -a "$1,0,0,0,$2" >/dev/null &
    else
        echo "stress test is not supported in $GPU_TYPE."
    fi
    cd $ROOT_DIR || exit
}

get_system_disk() {
    # 首先尝试从根分区获取
    local root_part=$(df / | awk 'NR==2 {print $1}')
    local disk_name=""

    # 检查是否是标准设备路径
    if [[ "$root_part" =~ ^/dev/(sd|nvme|vd) ]]; then
        # 提取设备名称（移除分区号）
        disk_name=$(echo "$root_part" | sed -E 's|/dev/||; s/[0-9]+$//')
    else
        # 如果根分区不是标准设备，尝试从/boot获取
        root_part=$(df /boot 2>/dev/null | awk 'NR==2 {print $1}')
        if [[ "$root_part" =~ ^/dev/(sd|nvme|vd) ]]; then
            disk_name=$(echo "$root_part" | sed -E 's|/dev/||; s/[0-9]+$//')
        else
            # 如果仍然找不到，返回一个安全的默认值
            echo "sda"
            return
        fi
    fi

    echo "$disk_name"
}

# 存储压力测试
storage_stress_test() {
    local load=$1
    local duration=$2
    local file_size="100G"
    local is_last_level=$3  # 新增参数，标识是否为最后一档压力测试

    # 使用新函数获取系统盘
    local os_disk="/dev/$(get_system_disk)"
    local all_disks
    all_disks=$(lsblk -dno NAME,TYPE | grep disk | awk '{print $1}' | sort)

    local total_disks=0  # 初始化为0
    local tested_disks=0  # 初始化为0
    local disks_to_test=()

    for disk in ${all_disks}; do
        if [[ "/dev/${disk}" != "${os_disk}" ]]; then
            disks_to_test+=("${disk}")
            ((total_disks++))
        fi
    done

    local disks_count=${#disks_to_test[@]}
    local disks_to_test_count=0  # 初始化为0

    # 如果是最后一档压力测试，则测试所有非系统盘
    if [ "${is_last_level}" -eq 1 ]; then
        log "最后一档压力测试：测试所有非系统盘"
        disks_to_test_count=$total_disks
    elif [ "${total_disks:-0}" -gt 0 ] && [ "${load:-0}" -gt 0 ]; then
        disks_to_test_count=$(echo "scale=0; (${total_disks} * ${load} / 100 + 0.5)/1" | bc)
    fi

    fio_cmd="fio --name=test --ioengine=libaio --direct=1 --rw=randread --bs=4k --iodepth=32 --numjobs=32 --time_based --runtime=${duration}"

    for ((i = 0; i < disks_to_test_count && i < disks_count; i++)); do
        disk=${disks_to_test[i]}
        fio_cmd+=" --filename=/dev/${disk} --size=${file_size}"
        ((tested_disks++))
    done

    # 增加详细日志输出
    if [ "${tested_disks}" -gt 0 ]; then
        log "执行 fio 测试，测试磁盘数: ${tested_disks}/${total_disks}"
        ${fio_cmd} > "${RESULT_LOCATION}/fio_test.log" 2>&1 &
    else
        tested_disks=0
    fi
    # 只返回纯数字，不包含任何日志信息
    echo "${tested_disks},${total_disks}"
}

# 新增获取CPU利用率的函数
get_cpu_usage() {
    # 使用top命令获取CPU使用率，-b批处理模式，-n1只采样一次
    local cpu_usage
    cpu_usage=$(top -b -n2 | grep '%Cpu' | tail -n1 | awk '{print $2}')
    # 如果结果小于1，转换成正确的格式（比如从.1变成0.1）
    if [[ "$cpu_usage" =~ ^\. ]]; then
        cpu_usage="0$cpu_usage"
    fi
    echo "$cpu_usage"
}


# 压力测试
start_stress_test() {
    local load=$1
    local GPU_flag=$2
    log "开始执行 ${load}% 负载的压力测试"
    # 检查是否为最后一档压力测试
    local is_last_level=0
    if [ "${load}" -eq "${LOAD_LEVELS[-1]}" ]; then
        is_last_level=1
        log "当前为最后一档压力测试 (${load}%)"
    fi
    if [ "$load" -eq 0 ]; then
        log "0% 负载，跳过 stress-ng 和 fio 测试"
        sleep "${STRESS_DURATION}"
        return
    fi
    local cpu_load

    # 修改这里：最后一档时保留一个核心不被压测
    if [ "${is_last_level}" -eq 1 ]; then
        cpu_load=$((cpu_cores - 1))
        log "最后一档压力测试：保留一个CPU核心，实际使用${cpu_load}个核心"
    else
        cpu_load=$(echo "scale=0; ($cpu_cores * $load / 100 - 0.5)/1" | bc)
        cpu_load=${cpu_load:-1}  # 如果计算结果为空，设置为1
    fi

    # 修改内存负载计算，将最大内存使用率从90%提高到95%
    local memory_load
    local max_memory_percent=95  # 最大内存使用百分比修改为95%
    local target_memory_percent

    # 计算目标内存百分比，取负载百分比和最大内存百分比的较小值
    if [ "$load" -gt "$max_memory_percent" ]; then
        target_memory_percent=$max_memory_percent
        log "内存负载限制为${max_memory_percent}%，原始负载为${load}%"
    else
        target_memory_percent=$load
    fi

    # 计算内存负载大小（MB）
    memory_load=$(echo "scale=0; $memory_size * $target_memory_percent / 100" | bc)
    memory_load=${memory_load:-100}  # 如果计算结果为空，设置为100M

    # 计算每个VM实例的内存大小
    local vm_count=$(nproc)
    local vm_bytes_per_instance
    vm_bytes_per_instance=$(echo "scale=0; $memory_load / $vm_count" | bc)
    vm_bytes_per_instance=${vm_bytes_per_instance:-16}  # 确保每个实例至少有16MB

    if [ "$load" -ne 0 ]; then
        # 优化内存压力测试：使用多个VM实例和vm-method all参数，但限制总内存使用
        log "执行命令: stress-ng --cpu ${cpu_load} --vm ${vm_count} --vm-bytes ${vm_bytes_per_instance}M --vm-method all --vm-hang 0"
        stress-ng --cpu "${cpu_load}" --vm ${vm_count} --vm-bytes ${vm_bytes_per_instance}M --vm-method all --vm-hang 0 >/dev/null 2>&1 &
        local STRESS_PID=$!
    fi

    local disk_info
    disk_info=$(storage_stress_test "${load}" "${STRESS_DURATION}" "${is_last_level}")

    tested_disks=$(echo "${disk_info}" | awk -F',' '{print $1}')
    total_disks=$(echo "${disk_info}" | awk -F',' '{print $2}')

    # 确保变量是整数
    tested_disks=${tested_disks:-0}
    total_disks=${total_disks:-0}

    log "存储压测情况: 测试磁盘数 ${tested_disks}, 目标压测总磁盘数 ${total_disks}"

    # 在最后一档压力测试中启动网卡压测（无论是否有GPU）
    if [ "${is_last_level}" -eq 1 ]; then
        network_stress_test "${STRESS_DURATION}" &
        log "已启动网卡压力测试"
    fi

    # GPU的压测放到这里
    # 判断是否最后一档且有GPU
    if [ "${is_last_level}" -eq 1 ] && [ "${GPU_flag}" -eq 1 ]; then
        log "最后一档加上GPU压力测试"
        GPU_Stress_Test $STRESS_DURATION 1 >/dev/null 2>&1 &
    sleep 10s
    fi

    local TARGET_LOAD=${load}
    local TOLERANCE=60
    local UPPER_BOUND
    UPPER_BOUND=$(echo "scale=0; ${TARGET_LOAD} + (10 * ${TOLERANCE} / 100)" | bc)
    local LOWER_BOUND
    LOWER_BOUND=$(echo "scale=0; ${TARGET_LOAD} - (10 * ${TOLERANCE} / 100)" | bc)
    log "Upper bound is ${UPPER_BOUND}%, Lower bound is ${LOWER_BOUND}%"

    sleep 30s
    local attempt=0
    local MAX_ATTEMPTS=5
    while [ "${attempt}" -lt "${MAX_ATTEMPTS}" ]; do
        # 替换原有的CPU负载计算方法
        local CPU_LOAD
        CPU_LOAD=$(get_cpu_usage)

        if [[ "${CPU_LOAD}" =~ ^[0-9]+([.][0-9]+)?$ ]] &&
            [[ "${LOWER_BOUND}" =~ ^[0-9]+([.][0-9]+)?$ ]] &&
            [[ "${UPPER_BOUND}" =~ ^[0-9]+([.][0-9]+)?$ ]]; then
            if (($(echo "${CPU_LOAD} >= ${LOWER_BOUND} && ${CPU_LOAD} <= ${UPPER_BOUND}" | bc))); then
                log "CPU负载已经到达目标范围内：${CPU_LOAD}%"
                break
            else
                log "CPU负载未达到目标范围：${CPU_LOAD}%"
                sleep 1s
            fi
        else
            log "无效的 CPU 负载或边界值: CPU_LOAD=${CPU_LOAD}, LOWER_BOUND=${LOWER_BOUND}, UPPER_BOUND=${UPPER_BOUND}"
            sleep 1s
        fi
        ((attempt++))
    done

    log "开始采集功耗数据"
    local REQUIRED_SAMPLES=10  # 修改为10次采样
    local sample_count=0
    while [ "${sample_count}" -lt "${REQUIRED_SAMPLES}" ]; do
        local power
        power=$(get_power_reading)
        power=${power##*$'\n'}
        log "当前功耗: ${power}W"
        echo "${load},${power},${memory_load},${disk_util}" >>"${RESULT_LOCATION}/System_Power.csv"
        ((sample_count++))
        if [ "${sample_count}" -lt "${REQUIRED_SAMPLES}" ]; then
            sleep 10s
        fi
    done

    log "开始收集频率数据"
    collect_freq "${load}"

    log "清理压力测试进程"
    if [ -n "$STRESS_PID" ]; then
        kill $STRESS_PID 2>/dev/null
        log "已终止 stress-ng 进程 (PID: $STRESS_PID)"
    else
        log "没有找到 stress-ng 进程 PID"
    fi

    if pgrep stress-ng >/dev/null; then
        killall stress-ng
        log "已终止所有 stress-ng 进程"
    else
        log "没有找到运行中的 stress-ng 进程"
    fi

    if pgrep fio >/dev/null; then
        killall fio
        log "已终止所有 fio 进程"
    else
        log "没有找到运行中的 fio 进程"
    fi

    # 清理网卡压测进程（如果是最后一档）
    if [ "${is_last_level}" -eq 1 ]; then
        cleanup_network_test
    fi
    log "${load}% 负载的压力测试完成"
    sleep 10s
    log "压力测试清理完成，准备进行下一次测试"
}

# 终止进程函数
kill_process() {
    local process_name=$1
    local max_attempts=3
    local attempt=1

    log "尝试终止 ${process_name} 进程..."

    while [ $attempt -le $max_attempts ]; do
        if pgrep "${process_name}" >/dev/null; then
            log "第 ${attempt} 次尝试终止 ${process_name} 进程"

            case $attempt in
                1)
                    # 第一次尝试：使用 SIGTERM
                    killall "${process_name}" 2>/dev/null
                    ;;
                2)
                    # 第二次尝试：使用 SIGKILL
                    killall -9 "${process_name}" 2>/dev/null
                    ;;
                3)
                    # 第三次尝试：逐个终止进程并重置设备
                    for pid in $(pgrep "${process_name}"); do
                        kill -9 "$pid" 2>/dev/null
                    done

                    # 对于 fio 进程特殊处理
                    if [ "${process_name}" = "fio" ]; then
                        log "尝试重置所有 NVMe 设备..."

                        # 同步文件系统
                        sync

                        # 清理缓存
                        echo 3 > /proc/sys/vm/drop_caches

                        # 重置所有 NVMe 设备
                        for nvme in /dev/nvme*; do
                            if [ -b "$nvme" ]; then
                                log "重置设备: $nvme"
                                nvme reset "$nvme" 2>/dev/null || true
                            fi
                        done

                        # 等待设备重置完成
                        sleep 2
                    fi
                    ;;
            esac

            # 等待进程退出
            sleep 2

            # 检查进程是否还在运行
            if ! pgrep "${process_name}" >/dev/null; then
                log "${process_name} 进程已成功终止"
                return 0
            fi

            ((attempt++))
        else
            log "未发现运行中的 ${process_name} 进程"
            return 0
        fi
    done

    # 如果进程仍然存在
    if pgrep "${process_name}" >/dev/null; then
        log "警告: 无法终止所有 ${process_name} 进程"
        ps aux | grep "${process_name}" | grep -v grep

        if [ "${process_name}" = "fio" ]; then
            log "建议执行以下操作："
            log "1. 尝试重启 Docker 容器"
            log "2. 如果问题持续，可能需要重启主机"
        fi

        return 1
    fi
}

# 生成测试报告
generate_test_report() {
    local report_file="${RESULT_LOCATION}/PowerTestReport.md"
    local test_date=$(date '+%Y-%m-%d %H:%M:%S')

    # 读取系统信息
    local system_info=$(cat "${RESULT_LOCATION}/SystemInfo.log")

    # 计算平均功耗
    calculate_average_power() {
        local load=$1
        awk -F',' -v target="$load" '
        $1 == target {
            sum += $2
            count++
        }
        END {
            if (count > 0) printf "%.1f", sum/count
            else print "N/A"
        }' "${RESULT_LOCATION}/System_Power.csv"
    }

    # 计算idle档最小功耗
    calculate_idle_min_power() {
        awk -F',' '$1==0{if(min==""||$2<min)min=$2}END{if(min!="")print min;else print "N/A"}' "${RESULT_LOCATION}/System_Power.csv"
    }

    # 创建报告
    cat << EOF > "${report_file}"
# 服务器功耗压力测试报告

## 测试信息
- 测试时间: ${test_date}
- 测试持续时间: ${STRESS_DURATION} 秒/档位
- 测试档位数量: ${#LOAD_LEVELS[@]} 个档位

## 系统配置
${system_info}

## 测试项目
1. CPU 压力测试
   - 处理器核心数: ${cpu_cores}
   - 测试负载等级: ${LOAD_LEVELS[*]}%

2. 内存压力测试
   - 可用内存: ${memory_size}MB

3. 存储设备测试
   - NVMe 数量: ${nvme_nums}
   - HDD 数量: ${hdd_disks}

4. GPU 测试状态
   - GPU 类型: ${GPU_TYPE:-"未检测到"}
   - GPU 压力测试: $([ "${GPU_DEVICE}" -eq 1 ] && echo "已执行" || echo "未执行")

## 测试结果
### 功耗数据统计
| 负载等级 | 功耗(W) |
|----------|-------------|
EOF

    # 添加每个负载等级的功耗数据
    for load in "${LOAD_LEVELS[@]}"; do
        if [ "$load" -eq 0 ]; then
            # idle档，取最小值
            local idle_power=$(calculate_idle_min_power)
            echo "| 0% | $idle_power |" >> "${report_file}"
        else
            local avg_power=$(calculate_average_power $load)
            echo "| ${load}% | ${avg_power} |" >> "${report_file}"
        fi
    done

    # 添加功耗曲线图
    cat << EOF >> "${report_file}"

### 功耗曲线图
![功耗曲线]:${RESULT_LOCATION}/power_curve.png

## 测试结论
1. 功耗变化趋势：从空载到满载，功耗呈阶梯式上升
2. 系统稳定性：在各个负载等级下系统运行稳定
3. 测试完整性：已完成所有计划的压力测试项目

## 备注
- 测试数据已保存在 PowerData_$(date +%Y%m%d).tar.gz 中
- 详细的测试日志可在 script.log 中查看
EOF

    log "测试报告已生成: ${report_file}"
}

# 主函数
main() {
    init
    system_info
    local TestDate
    TestDate=$(date +%Y%m%d)

    if pgrep stress-ng >/dev/null; then
        killall stress-ng
        echo "Terminated stress-ng processes"
    else
        echo "No stress-ng processes found running"
    fi

    if pgrep fio >/dev/null; then
        killall fio
        echo "Terminated fio processes"
    else
        echo "No fio processes found running"
    fi

    cd /home || handle_error 4 "无法切换到 /home 目录"
    cpu_cores=$(nproc)
    memory_size=$(free -m | awk '/^Mem:/{print $2}')
    memory_size=$((memory_size - 1000))
    sn=$(dmidecode -t 1 | grep "Serial Number" | awk '{print $3}')
    nvme_disks=$(lsblk -dno NAME,TYPE | grep nvme | awk '{print $1}')
    nvme_nums=$(lsblk -dno NAME,TYPE | grep nvme | awk '{print $1}' | wc -l)
    hdd_disks=$(lsblk -dno NAME,TYPE | grep sd | awk '{print $1}' | wc -l)
    nvme_paths=""
    for disk in ${nvme_disks}; do
        nvme_paths+="/dev/${disk}:"
    done
    nvme_paths=${nvme_paths%:}

    detect_gpu_type
    if [ $? -ne 0 ]; then
        echo "The GPU type is not in the support list. (NVIDIA, Ascend, Cambricon, Goldwasser)"
        GPU_DEVICE=0 #没有发现GPU
    else
        echo "The GPU type is in the support list."
        GPU_DEVICE=1 #有GPU
    fi
    echo "cpuUtil,power,memUtil,diskUtil" >"${RESULT_LOCATION}/System_Power.csv"

    log "CPU cores:${cpu_cores}, Mem size:${memory_size}, NVMes:${nvme_nums}, HDDs:${hdd_disks} Machine SN:${sn}"
    echo "cpuUtil,freq,coreIdx" >"${RESULT_LOCATION}/Fre_suma.csv"

    sample_idle_power

    for load in "${LOAD_LEVELS[@]}"; do
        log "开始执行 ${load}% 负载的压力测试"
        if ! start_stress_test "${load}" ${GPU_DEVICE}; then
            log "错误: ${load}% 负载的压力测试失败"
            # 可以选择在这里添加错误处理逻辑，比如退出脚本或继续下一次测试
        fi
        log "${load}% 负载的压力测试完成"
    done

    log "所有负载等级的压力测试已完成"

    generate_power_graph
    generate_test_report

    log "测试完成，时间: $(date +%Y%m%d_%H:%M:%S)"
    cd "${RESULT_LOCATION}" || handle_error 4 "无法切换到结果目录"
    log "开始打包测试结果..."
    tar -czf "PowerData_${TestDate}.tar.gz" ./*.csv ./*.png ./*.md ./*.log
    log "测试结果已打包为: PowerData_${TestDate}.tar.gz"
    sleep 1
   # rm -f ./*.log ./*.csv ./*.png
}

main
