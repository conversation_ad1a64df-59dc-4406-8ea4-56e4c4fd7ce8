#!/bin/sh
#===============================================================================
# Copyright 2001-2022 Intel Corporation.
#
# This software and the related documents are Intel copyrighted  materials,  and
# your use of  them is  governed by the  express license  under which  they were
# provided to you (License).  Unless the License provides otherwise, you may not
# use, modify, copy, publish, distribute,  disclose or transmit this software or
# the related documents without Intel's prior written permission.
#
# This software and the related documents  are provided as  is,  with no express
# or implied  warranties,  other  than those  that are  expressly stated  in the
# License.
#===============================================================================

echo "This is a SAMPLE run script for running a shared-memory version of"
echo "Intel(R) Distribution for LINPACK* Benchmark. Change it to reflect"
echo "the correct number of CPUs/threads, problem input files, etc.."
echo "*Other names and brands may be claimed as the property of others."

# Setting up affinity for better threading performance
export KMP_AFFINITY=nowarnings,compact,1,0,granularity=fine

# Use numactl for better performance on multi-socket machines.
nnodes=`numactl -H 2>&1 | awk '/available:/ {print $2}'`
cpucores=`cat /proc/cpuinfo | awk '/cpu cores/ {print $4; exit}'`

if [  $nnodes -gt 1 -a $cpucores -gt 8 ]
then
    numacmd="numactl --interleave=all"
else
    numacmd=
fi

arch=xeon32
{
  date
  $numacmd ./xlinpack_$arch lininput_$arch
  echo -n "Done: "
  date
} | tee lin_$arch.txt

