#!/bin/bash

while getopts i:d:a: opt;
do
  case $opt in
  i)
    task_id=$OPTARG
    echo "task_id : $task_id"
   ;;
  d)
    domain=$OPTARG
    echo "domain : $domain"
   ;;
  a)
    para=$OPTARG
    echo "para : $para"
   ;;
  esac
done

specjvm_install()
{
yum -y install dejavu-lgc*
yum -y  install  java* --skip-broken
mkdir -p /home/<USER>/home/<USER>/home/<USER>/
rm -rf /home/<USER>/SPECjvm* /home/<USER>/*
cd /home/<USER>
wget $domain/saturn/testTool?toolName=SPECjvm2008.zip -O SPECjvm2008.zip
cd /home/<USER>
wget $domain/saturn/testTool?toolName=SPECjvm2008.zip -O SPECjvm2008.zip
unzip SPECjvm2008.zip
chmod +x *.sh
}

specjvm_test()
{
rm -rf /home/<USER>/specjvm*
cd /home/<USER>/
JAVA_HOME=/usr/lib/jvm/java-1.7.0;export JAVA_HOME;
./run-specjvm.sh startup.helloworld -ikv
if [ $? -eq 0 ]
then
    ./run-specjvm.sh
else
    echo "specjvm run error!" >> specjvm.log
    exit
fi
cat results/SPECjvm2008.002/SPECjvm2008.002.html |grep "Composite result:"|head -1|awk '{print $7}' > specjvm.log
mv specjvm.log /home/<USER>/
}

callback()
{
cd /home/<USER>
specjvm=`cat specjvm.log`
curl -X POST  -F "taskId=$task_id" -F 'taskIndexListStr=[{"indexTag":"specjvm","indexValue":"'"$specjvm"'"}]' "$domain/saturn/script/callback"
}

specjvm_install
specjvm_test
callback

##下面代码的功能是反馈脚本执行异常
if [ $? -eq 0 ];
then
echo "脚本执行成功！"
else
echo "******给saturn系统反馈脚本执行失败******"
curl "$domain/saturn/script/failure?taskId=$task_id"
fi