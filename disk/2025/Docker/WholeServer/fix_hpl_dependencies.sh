#!/bin/bash

# HPL依赖修复脚本
# 用于安装ARM平台HPL编译所需的所有依赖

export LANG=en_US.UTF-8

echo "=========================================="
echo "HPL依赖修复脚本"
echo "=========================================="

# 检测系统类型
if [[ "$OSTYPE" == "darwin"* ]]; then
    echo "[INFO] 检测到macOS系统"
    SYSTEM="macos"
elif command -v yum >/dev/null 2>&1; then
    echo "[INFO] 检测到基于yum的Linux系统"
    SYSTEM="yum"
elif command -v apt-get >/dev/null 2>&1; then
    echo "[INFO] 检测到基于apt的Linux系统"
    SYSTEM="apt"
else
    echo "[ERROR] 无法识别的系统类型"
    exit 1
fi

# 安装基本依赖
echo -e "\n[INFO] 安装基本编译依赖..."
case "$SYSTEM" in
    "macos")
        # macOS使用Homebrew
        if ! command -v brew >/dev/null 2>&1; then
            echo "[ERROR] 需要安装Homebrew"
            echo "请运行: /bin/bash -c \"\$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)\""
            exit 1
        fi
        
        brew install gcc gfortran cmake wget open-mpi openblas
        ;;
    "yum")
        yum install -y gcc gcc-c++ gcc-gfortran make cmake wget tar bzip2 unzip perl perl-Data-Dumper
        ;;
    "apt")
        apt-get update
        apt-get install -y gcc g++ gfortran make cmake wget tar bzip2 unzip perl build-essential
        ;;
esac

echo "✓ 基本依赖安装完成"

# 对于Linux系统，需要手动编译安装OpenMPI和OpenBLAS
if [[ "$SYSTEM" != "macos" ]]; then
    
    # 创建构建目录
    BUILD_DIR="/tmp/hpl_deps_build"
    rm -rf "$BUILD_DIR"
    mkdir -p "$BUILD_DIR"
    cd "$BUILD_DIR" || exit 1
    
    echo -e "\n[INFO] 编译安装OpenMPI..."
    
    # 下载OpenMPI（使用本地文件或网络下载）
    OPENMPI_FILE="openmpi-4.0.7.tar.gz"
    OPENMPI_URL="https://download.open-mpi.org/release/open-mpi/v4.0/openmpi-4.0.7.tar.gz"
    
    # 尝试从本地工具目录获取
    if [ -f "/Users/<USER>/Documents/test/Script/SaturnScript/npi-test/disk/2025/Docker/WholeServer/tool/$OPENMPI_FILE" ]; then
        echo "[INFO] 使用本地OpenMPI文件"
        cp "/Users/<USER>/Documents/test/Script/SaturnScript/npi-test/disk/2025/Docker/WholeServer/tool/$OPENMPI_FILE" .
    else
        echo "[INFO] 下载OpenMPI..."
        if ! wget --timeout=300 "$OPENMPI_URL"; then
            echo "[ERROR] OpenMPI下载失败"
            exit 1
        fi
    fi
    
    tar -zxf "$OPENMPI_FILE"
    cd openmpi-4.0.7 || exit 1
    
    # 配置OpenMPI
    MPI_PREFIX="/home/<USER>/OpenMPI"
    mkdir -p "$MPI_PREFIX"
    
    ./configure --prefix="$MPI_PREFIX" \
                --enable-pretty-print-stacktrace \
                --enable-orterun-prefix-by-default \
                --with-cma \
                --enable-mpi1-compatibility
    
    if [ $? -ne 0 ]; then
        echo "[ERROR] OpenMPI配置失败"
        exit 1
    fi
    
    # 编译和安装
    make -j$(nproc)
    if [ $? -ne 0 ]; then
        echo "[ERROR] OpenMPI编译失败"
        exit 1
    fi
    
    make install
    if [ $? -ne 0 ]; then
        echo "[ERROR] OpenMPI安装失败"
        exit 1
    fi
    
    echo "✓ OpenMPI安装完成"
    
    # 设置环境变量
    export MPI_HOME="$MPI_PREFIX"
    export PATH="$MPI_HOME/bin:$PATH"
    export LD_LIBRARY_PATH="$MPI_HOME/lib:$LD_LIBRARY_PATH"
    
    cd "$BUILD_DIR" || exit 1
    
    echo -e "\n[INFO] 编译安装OpenBLAS..."
    
    # 下载OpenBLAS
    OPENBLAS_FILE="OpenBLAS-0.3.6.zip"
    OPENBLAS_URL="https://github.com/xianyi/OpenBLAS/archive/v0.3.6.zip"
    
    if [ -f "/Users/<USER>/Documents/test/Script/SaturnScript/npi-test/disk/2025/Docker/WholeServer/tool/$OPENBLAS_FILE" ]; then
        echo "[INFO] 使用本地OpenBLAS文件"
        cp "/Users/<USER>/Documents/test/Script/SaturnScript/npi-test/disk/2025/Docker/WholeServer/tool/$OPENBLAS_FILE" .
    else
        echo "[INFO] 下载OpenBLAS..."
        if ! wget --timeout=300 "$OPENBLAS_URL" -O "$OPENBLAS_FILE"; then
            echo "[ERROR] OpenBLAS下载失败"
            exit 1
        fi
    fi
    
    unzip "$OPENBLAS_FILE"
    cd OpenBLAS-0.3.6 || exit 1
    
    # 编译OpenBLAS
    make -j$(nproc) TARGET=ARMV8
    if [ $? -ne 0 ]; then
        echo "[ERROR] OpenBLAS编译失败"
        exit 1
    fi
    
    # 安装OpenBLAS
    OPENBLAS_PREFIX="/home/<USER>/OpenBLAS"
    mkdir -p "$OPENBLAS_PREFIX"
    make install PREFIX="$OPENBLAS_PREFIX"
    if [ $? -ne 0 ]; then
        echo "[ERROR] OpenBLAS安装失败"
        exit 1
    fi
    
    echo "✓ OpenBLAS安装完成"
    
    # 清理构建目录
    cd /
    rm -rf "$BUILD_DIR"
    
else
    # macOS系统设置环境变量
    export MPI_HOME="/opt/homebrew"  # 或者 /usr/local，取决于Homebrew安装位置
    export PATH="$MPI_HOME/bin:$PATH"
    export LD_LIBRARY_PATH="$MPI_HOME/lib:$LD_LIBRARY_PATH"
fi

echo -e "\n[INFO] 验证安装结果..."

# 验证工具
echo "验证编译工具:"
for tool in gcc g++ gfortran make cmake wget; do
    if command -v "$tool" >/dev/null 2>&1; then
        echo "✓ $tool: $(command -v $tool)"
    else
        echo "✗ $tool: 未找到"
    fi
done

# 验证MPI
echo -e "\n验证MPI:"
if command -v mpicc >/dev/null 2>&1; then
    echo "✓ mpicc: $(mpicc --version | head -1)"
else
    echo "✗ mpicc: 未找到"
fi

# 验证BLAS
echo -e "\n验证BLAS库:"
if [[ "$SYSTEM" == "macos" ]]; then
    BLAS_PATH="/opt/homebrew/lib"  # 或 /usr/local/lib
else
    BLAS_PATH="/home/<USER>/OpenBLAS/lib"
fi

if [ -d "$BLAS_PATH" ]; then
    echo "✓ BLAS库路径: $BLAS_PATH"
    ls -la "$BLAS_PATH"/libopenblas* 2>/dev/null || echo "BLAS库文件未找到"
else
    echo "✗ BLAS库路径不存在: $BLAS_PATH"
fi

echo -e "\n=========================================="
echo "依赖修复完成"
echo "=========================================="

# 输出环境变量设置建议
echo -e "\n[INFO] 建议将以下环境变量添加到您的shell配置文件中:"
if [[ "$SYSTEM" != "macos" ]]; then
    echo "export MPI_HOME=/home/<USER>/OpenMPI"
    echo "export PATH=\$MPI_HOME/bin:\$PATH"
    echo "export LD_LIBRARY_PATH=\$MPI_HOME/lib:/home/<USER>/OpenBLAS/lib:\$LD_LIBRARY_PATH"
else
    echo "export MPI_HOME=/opt/homebrew"
    echo "export PATH=\$MPI_HOME/bin:\$PATH"
    echo "export LD_LIBRARY_PATH=\$MPI_HOME/lib:\$LD_LIBRARY_PATH"
fi

echo -e "\n现在可以重新运行 debug_hpl_build.sh 来测试HPL编译"
