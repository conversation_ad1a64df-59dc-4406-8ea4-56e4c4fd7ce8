#!/bin/bash

# HPL编译调试脚本
# 用于诊断和修复ARM平台HPL编译问题

export LANG=en_US.UTF-8

echo "=========================================="
echo "HPL编译调试脚本"
echo "=========================================="

# 检查基本环境
echo "[INFO] 检查基本环境..."
echo "架构: $(uname -m)"
echo "内核: $(uname -r)"
echo "发行版: $(cat /etc/os-release 2>/dev/null | grep PRETTY_NAME | cut -d= -f2 | tr -d '\"' || echo 'Unknown')"

# 检查编译工具
echo -e "\n[INFO] 检查编译工具..."
for tool in gcc g++ gfortran make cmake wget tar; do
    if command -v "$tool" >/dev/null 2>&1; then
        echo "✓ $tool: $(command -v $tool)"
    else
        echo "✗ $tool: 未安装"
    fi
done

# 检查MPI环境
echo -e "\n[INFO] 检查MPI环境..."
MPI_HOME="/home/<USER>/OpenMPI"
if [ -d "$MPI_HOME" ]; then
    echo "✓ MPI安装目录存在: $MPI_HOME"
    export PATH="$MPI_HOME/bin:$PATH"
    export LD_LIBRARY_PATH="$MPI_HOME/lib:$LD_LIBRARY_PATH"
    
    if command -v mpicc >/dev/null 2>&1; then
        echo "✓ mpicc可用: $(mpicc --version | head -1)"
    else
        echo "✗ mpicc不可用"
    fi
else
    echo "✗ MPI安装目录不存在: $MPI_HOME"
fi

# 检查BLAS库
echo -e "\n[INFO] 检查BLAS库..."
OPENBLAS_PATH="/home/<USER>/OpenBLAS"
KML_PATH="/usr/lib64/kml"

if [ -d "$KML_PATH" ]; then
    echo "✓ 找到KML库: $KML_PATH"
    BLAS_LIB="$KML_PATH"
    BLAS_FLAGS="-L$KML_PATH -lkml"
elif [ -d "$OPENBLAS_PATH" ]; then
    echo "✓ 找到OpenBLAS库: $OPENBLAS_PATH"
    BLAS_LIB="$OPENBLAS_PATH/lib"
    BLAS_FLAGS="-L$OPENBLAS_PATH/lib -lopenblas"
else
    echo "✗ 未找到BLAS库"
    exit 1
fi

echo "使用BLAS库: $BLAS_LIB"
echo "BLAS编译标志: $BLAS_FLAGS"

# 检查HPL源码
echo -e "\n[INFO] 检查HPL源码..."
HPL_BUILD_DIR="/home/<USER>/hpl_build"
HPL_DIR="$HPL_BUILD_DIR/hpl-2.3"

if [ -d "$HPL_DIR" ]; then
    echo "✓ HPL目录存在: $HPL_DIR"
    cd "$HPL_DIR" || exit 1
    
    # 检查关键文件
    for file in Make.top src/auxil src/blas testing/ptest; do
        if [ -e "$file" ]; then
            echo "✓ $file 存在"
        else
            echo "✗ $file 不存在"
        fi
    done
else
    echo "✗ HPL目录不存在: $HPL_DIR"
    echo "[INFO] 尝试重新下载和解压HPL..."
    
    mkdir -p "$HPL_BUILD_DIR"
    cd "$HPL_BUILD_DIR" || exit 1
    
    # 下载HPL
    HPL_URL="http://10.8.104.100/huzz/DOCKER/tools/hpl-2.3.tar.gz"
    if wget --timeout=60 "$HPL_URL" -O hpl-2.3.tar.gz; then
        echo "✓ HPL下载成功"
        tar -zxf hpl-2.3.tar.gz
        if [ -d "hpl-2.3" ]; then
            echo "✓ HPL解压成功"
            cd hpl-2.3 || exit 1
        else
            echo "✗ HPL解压失败"
            exit 1
        fi
    else
        echo "✗ HPL下载失败"
        exit 1
    fi
fi

# 创建优化的Make配置
echo -e "\n[INFO] 创建Make.Linux_ARM配置文件..."
cat > Make.Linux_ARM << EOF
SHELL        = /bin/sh
CD           = cd
CP           = cp
LN_S         = ln -fs
MKDIR        = mkdir -p
RM           = /bin/rm -f
TOUCH        = touch
ARCH         = Linux_ARM
TOPdir       = $(pwd)
INCdir       = \$(TOPdir)/include
BINdir       = \$(TOPdir)/bin/\$(ARCH)
LIBdir       = \$(TOPdir)/lib/\$(ARCH)
HPLlib       = \$(LIBdir)/libhpl.a
MPdir        = $MPI_HOME
MPinc        = -I\$(MPdir)/include
MPlib        = -L\$(MPdir)/lib -lmpi
LAdir        = $BLAS_LIB
LAinc        = 
LAlib        = $BLAS_FLAGS
F2CDEFS      = -DAdd__ -DF77_INTEGER=int -DStringSunStyle
HPL_OPTS     = -DHPL_CALL_CBLAS
HPL_INCLUDES = -I\$(INCdir) -I\$(INCdir)/\$(ARCH) \$(LAinc) \$(MPinc)
HPL_LIBS     = \$(HPLlib) \$(LAlib) \$(MPlib)
HPL_DEFS     = \$(F2CDEFS) \$(HPL_OPTS) \$(HPL_INCLUDES)
CC           = mpicc
CCNOOPT      = \$(HPL_DEFS)
CCFLAGS      = \$(HPL_DEFS) -fomit-frame-pointer -O3 -funroll-loops -W -Wall
LINKER       = mpicc
LINKFLAGS    = \$(CCFLAGS)
ARCHIVER     = ar
ARFLAGS      = r
RANLIB       = echo
EOF

echo "✓ Make.Linux_ARM配置文件已创建"

# 测试MPI编译
echo -e "\n[INFO] 测试MPI编译环境..."
cat > test_mpi.c << 'EOF'
#include <mpi.h>
#include <stdio.h>
int main(int argc, char** argv) {
    MPI_Init(&argc, &argv);
    printf("MPI test successful\n");
    MPI_Finalize();
    return 0;
}
EOF

if mpicc -o test_mpi test_mpi.c 2>/dev/null; then
    echo "✓ MPI编译测试成功"
    rm -f test_mpi test_mpi.c
else
    echo "✗ MPI编译测试失败"
    echo "错误信息:"
    mpicc -o test_mpi test_mpi.c
    rm -f test_mpi test_mpi.c
    exit 1
fi

# 开始编译HPL
echo -e "\n[INFO] 开始编译HPL..."
echo "这可能需要几分钟时间..."

# 清理之前的编译
make clean arch=Linux_ARM 2>/dev/null || true

# 创建必要目录
mkdir -p bin/Linux_ARM lib/Linux_ARM include/Linux_ARM

# 设置Make.inc链接
find . -type d -name Linux_ARM | while IFS= read -r dir; do
    ln -fs ../../../Make.Linux_ARM "$dir/Make.inc" 2>/dev/null || true
done

# 编译
echo "[INFO] 执行编译命令: make arch=Linux_ARM"
if timeout 1800 make arch=Linux_ARM > build.log 2>&1; then
    echo "✓ HPL编译成功"
else
    echo "✗ HPL编译失败"
    echo "最后50行编译日志:"
    tail -50 build.log
    
    echo -e "\n[INFO] 尝试分步编译..."
    # 分步编译
    for target in src_lib src_test; do
        echo "编译目标: $target"
        if make $target arch=Linux_ARM 2>&1 | tail -20; then
            echo "✓ $target 编译成功"
        else
            echo "✗ $target 编译失败"
        fi
    done
fi

# 检查结果
echo -e "\n[INFO] 检查编译结果..."
if [ -f "bin/Linux_ARM/xhpl" ]; then
    echo "✓ xhpl可执行文件已生成: bin/Linux_ARM/xhpl"
    ls -la bin/Linux_ARM/xhpl
    
    # 测试可执行文件
    if ldd bin/Linux_ARM/xhpl >/dev/null 2>&1; then
        echo "✓ 动态库链接正常"
        echo "依赖库:"
        ldd bin/Linux_ARM/xhpl | head -10
    else
        echo "✗ 动态库链接可能有问题"
    fi
else
    echo "✗ xhpl可执行文件未生成"
    echo "搜索所有xhpl文件:"
    find . -name "xhpl" -type f 2>/dev/null || echo "未找到任何xhpl文件"
fi

echo -e "\n=========================================="
echo "调试完成"
echo "=========================================="
