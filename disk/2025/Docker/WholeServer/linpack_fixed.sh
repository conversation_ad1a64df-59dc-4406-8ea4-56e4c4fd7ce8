#!/bin/bash
export LANG=en_US.UTF-8

# Linpack测试脚本 - 支持ARM和x86平台（修复版本）
OUTPUT_DIR="/home/<USER>/linpack"
SOFTWARE_DIR="/home/<USER>"
LOG_FILE="$OUTPUT_DIR/linpack_run.log"
RESULT_LOG="$OUTPUT_DIR/linpack.log"
REPORT_MD="$OUTPUT_DIR/linpack_report.md"

mkdir -p "$OUTPUT_DIR" "$SOFTWARE_DIR"

# 检测架构
ARCH=$(uname -m)
echo "[INFO] 检测到架构: $ARCH" | tee -a "$LOG_FILE"

# 根据架构选择不同的处理方式
if [[ "$ARCH" == "aarch64" || "$ARCH" == "arm64" ]]; then
    echo "[INFO] ARM平台，需要编译HPL..." | tee -a "$LOG_FILE"
    LINPACK_DIR="/home/<USER>/linpack"
    
    # ARM平台处理逻辑
    arm_linpack_test() {
        echo "[INFO] 开始ARM平台Linpack测试..." | tee -a "$LOG_FILE"
        
        # 检查是否已经编译好HPL
        if [ -f "/home/<USER>/hpl_build/hpl-2.3/bin/Linux_ARM/xhpl" ]; then
            echo "[INFO] 发现已编译的HPL，直接使用..." | tee -a "$LOG_FILE"
            
            # 创建linpack目录并复制文件
            mkdir -p "$LINPACK_DIR"
            cp /home/<USER>/hpl_build/hpl-2.3/bin/Linux_ARM/xhpl "$LINPACK_DIR/"
            chmod +x "$LINPACK_DIR/xhpl"
            
            # 设置MPI环境
            export MPI_HOME="/home/<USER>/OpenMPI"
            export PATH="$MPI_HOME/bin:$PATH"
            export LD_LIBRARY_PATH="$MPI_HOME/lib:$LD_LIBRARY_PATH"
            
        else
            echo "[INFO] 需要编译HPL..." | tee -a "$LOG_FILE"
            
            # 1. 修复yum源
            echo "[INFO] 修复yum源..." | tee -a "$LOG_FILE"
            curl -o /etc/yum.repos.d/mtos-arm.repo http://10.8.104.100/mtos-arm.repo
            yum clean all && yum makecache
            
            # 2. 安装依赖
            echo "[INFO] 安装编译依赖..." | tee -a "$LOG_FILE"
            yum install -y gcc gcc-gfortran make wget tar
            
            # 3. 设置编译环境
            build_dir="$SOFTWARE_DIR/hpl_build"
            mkdir -p "$build_dir"
            cd "$build_dir"
            
            # 下载并安装OpenMPI
            if [ ! -d "/home/<USER>/OpenMPI" ]; then
                echo "[INFO] 下载并安装OpenMPI..." | tee -a "$LOG_FILE"
                wget http://10.8.104.100/huzz/DOCKER/tools/openmpi-4.1.1.tar.gz
                tar -zxf openmpi-4.1.1.tar.gz
                cd openmpi-4.1.1
                ./configure --prefix=/home/<USER>/OpenMPI --enable-mpi-fortran
                make -j$(nproc) && make install
                cd "$build_dir"
            fi
            
            # 设置MPI环境
            export MPI_HOME="/home/<USER>/OpenMPI"
            export PATH="$MPI_HOME/bin:$PATH"
            export LD_LIBRARY_PATH="$MPI_HOME/lib:$LD_LIBRARY_PATH"
            
            # 下载并安装OpenBLAS
            if [ ! -d "/home/<USER>/OpenBLAS" ]; then
                echo "[INFO] 下载并安装OpenBLAS..." | tee -a "$LOG_FILE"
                wget http://10.8.104.100/huzz/DOCKER/tools/OpenBLAS-0.3.18.tar.gz
                tar -zxf OpenBLAS-0.3.18.tar.gz
                cd OpenBLAS-0.3.18
                make -j$(nproc) PREFIX=/home/<USER>/OpenBLAS
                make install PREFIX=/home/<USER>/OpenBLAS
                cd "$build_dir"
            fi
            
            # 设置BLAS环境
            export BLAS_LIB="/home/<USER>/OpenBLAS"
            export BLAS_FLAGS="-L$BLAS_LIB/lib -lopenblas -lgfortran"
            
            # 4. 下载并编译HPL
            echo "[INFO] 下载并编译HPL..." | tee -a "$LOG_FILE"
            wget http://10.8.104.100/huzz/DOCKER/tools/hpl-2.3.tar.gz
            rm -rf hpl-2.3
            tar -zxf hpl-2.3.tar.gz
            cd hpl-2.3
            
            # 创建HPL配置文件
            cat > Make.Linux_ARM << EOF
SHELL        = /bin/sh
CD           = cd
CP           = cp
LN_S         = ln -fs
MKDIR        = mkdir -p
RM           = /bin/rm -f
TOUCH        = touch
ARCH         = Linux_ARM
TOPdir       = $(pwd)
INCdir       = \$(TOPdir)/include
BINdir       = \$(TOPdir)/bin/\$(ARCH)
LIBdir       = \$(TOPdir)/lib/\$(ARCH)
HPLlib       = \$(LIBdir)/libhpl.a
MPdir        = $MPI_HOME
MPinc        = -I\$(MPdir)/include
MPlib        = -L\$(MPdir)/lib -lmpi
LAdir        = $BLAS_LIB
LAinc        =
LAlib        = $BLAS_FLAGS
F2CDEFS      = -DAdd__ -DF77_INTEGER=int -DStringSunStyle
HPL_OPTS     = -DHPL_CALL_CBLAS
HPL_INCLUDES = -I\$(INCdir) -I\$(INCdir)/\$(ARCH) \$(LAinc) \$(MPinc)
HPL_LIBS     = \$(HPLlib) \$(LAlib) \$(MPlib)
HPL_DEFS     = \$(F2CDEFS) \$(HPL_OPTS) \$(HPL_INCLUDES)
CC           = mpicc
CCNOOPT      = \$(HPL_DEFS)
CCFLAGS      = \$(HPL_DEFS) -fomit-frame-pointer -O3 -funroll-loops
LINKER       = mpicc
LINKFLAGS    = \$(CCFLAGS)
ARCHIVER     = ar
ARFLAGS      = r
RANLIB       = echo
EOF
            
            # 编译HPL（限制时间避免无限循环）
            echo "[INFO] 开始HPL编译（最多30分钟）..." | tee -a "$LOG_FILE"
            mkdir -p bin/Linux_ARM lib/Linux_ARM include/Linux_ARM
            
            if timeout 1800 make arch=Linux_ARM 2>&1 | tee -a "$LOG_FILE"; then
                echo "[INFO] HPL编译成功" | tee -a "$LOG_FILE"
            else
                echo "[ERROR] HPL编译失败或超时" | tee -a "$LOG_FILE"
                exit 1
            fi
            
            # 验证编译结果
            if [ ! -f "bin/Linux_ARM/xhpl" ]; then
                echo "[ERROR] HPL可执行文件未生成" | tee -a "$LOG_FILE"
                exit 1
            fi
            
            # 复制到linpack目录
            mkdir -p "$LINPACK_DIR"
            cp bin/Linux_ARM/xhpl "$LINPACK_DIR/"
            chmod +x "$LINPACK_DIR/xhpl"
        fi
        
        # 创建HPL.dat配置文件（单进程配置）
        cat > "$LINPACK_DIR/HPL.dat" << 'EOF'
HPLinpack benchmark input file
Innovative Computing Laboratory, University of Tennessee
HPL.out      output file name (if any)
6            device out (6=stdout,7=stderr,file)
1            # of problems sizes (N)
1000         Ns
1            # of NBs
128          NBs
0            PMAP process mapping (0=Row-,1=Column-major)
1            # of process grids (P x Q)
1            Ps
1            Qs
16.0         threshold
1            # of panel fact
2            PFACTs (0=left, 1=Crout, 2=Right)
1            # of recursive stopping criterium
4            NBMINs (>= 1)
1            # of panels in recursion
2            NDIVs
1            # of recursive panel fact.
2            RFACTs (0=left, 1=Crout, 2=Right)
1            # of broadcast
1            BCASTs (0=1rg,1=1rM,2=2rg,3=2rM,4=Lng,5=LnM)
1            # of lookahead depth
1            DEPTHs (>=0)
2            SWAP (0=bin-exch,1=long,2=mix)
64           swapping threshold
0            L1 in (0=transposed,1=no-transposed) form
0            U  in (0=transposed,1=no-transposed) form
1            Equilibration (0=no,1=yes)
8            memory alignment in double (> 0)
EOF
        
        echo "[INFO] 开始HPL测试..." | tee -a "$LOG_FILE"
        cd "$LINPACK_DIR" || exit 1
        
        # 运行HPL测试（单进程，限制时间）
        if timeout 600 mpirun --allow-run-as-root -np 1 ./xhpl > "$OUTPUT_DIR/linpack_full.log" 2>&1; then
            echo "[INFO] HPL测试完成" | tee -a "$LOG_FILE"
        else
            echo "[ERROR] HPL测试失败或超时" | tee -a "$LOG_FILE"
            exit 1
        fi
    }
    
    # 调用ARM测试函数
    arm_linpack_test
    
else
    echo "[INFO] x86平台，使用预编译版本..." | tee -a "$LOG_FILE"
    LINPACK_DIR="/home/<USER>/linpack_amd"
    mkdir -p "$LINPACK_DIR"
    
    # 下载预编译的HPL二进制文件
    wget -O "$LINPACK_DIR/xhpl" http://10.8.104.100/huzz/DOCKER/tools/xhpl_amd_zen3 || exit 1
    chmod +x "$LINPACK_DIR/xhpl"
    
    # 创建HPL.dat配置文件
    cat > "$LINPACK_DIR/HPL.dat" << 'EOF'
HPLinpack benchmark input file
Innovative Computing Laboratory, University of Tennessee
HPL.out      output file name (if any)
6            device out (6=stdout,7=stderr,file)
1            # of problems sizes (N)
30000        Ns
1            # of NBs
256          NBs
0            PMAP process mapping (0=Row-,1=Column-major)
1            # of process grids (P x Q)
1            Ps
1            Qs
16.0         threshold
1            # of panel fact
2            PFACTs (0=left, 1=Crout, 2=Right)
1            # of recursive stopping criterium
4            NBMINs (>= 1)
1            # of panels in recursion
2            NDIVs
1            # of recursive panel fact.
2            RFACTs (0=left, 1=Crout, 2=Right)
1            # of broadcast
1            BCASTs (0=1rg,1=1rM,2=2rg,3=2rM,4=Lng,5=LnM)
1            # of lookahead depth
1            DEPTHs (>=0)
2            SWAP (0=bin-exch,1=long,2=mix)
64           swapping threshold
0            L1 in (0=transposed,1=no-transposed) form
0            U  in (0=transposed,1=no-transposed) form
1            Equilibration (0=no,1=yes)
8            memory alignment in double (> 0)
EOF
    
    # 运行HPL
    cd "$LINPACK_DIR"
    ./xhpl > "$OUTPUT_DIR/linpack_full.log" 2>&1
fi

# 提取结果（通用逻辑）
echo "[INFO] 提取测试结果..." | tee -a "$LOG_FILE"

average_result=""
max_result=""

# 显示测试输出用于调试
echo "[DEBUG] HPL测试输出:" | tee -a "$LOG_FILE"
cat "$OUTPUT_DIR/linpack_full.log" | tee -a "$LOG_FILE"

# HPL结果提取逻辑（修复版本）
if grep -q "WR.*R.*R" "$OUTPUT_DIR/linpack_full.log"; then
    echo "[INFO] 找到HPL测试结果..." | tee -a "$LOG_FILE"

    # 提取最后一次成功运行的性能数据（倒数第二列是Gflops）
    hpl_result=$(grep "WR.*R.*R" "$OUTPUT_DIR/linpack_full.log" | tail -1 | awk '{print $(NF-1)}')

    if [ -n "$hpl_result" ]; then
        # 处理科学计数法或普通数字
        if echo "$hpl_result" | grep -q "e"; then
            # 科学计数法转换
            max_result=$(echo "$hpl_result" | awk '{printf "%.4f", $1}')
        else
            # 普通数字
            max_result="$hpl_result"
        fi
        average_result="$max_result"
        echo "[INFO] HPL性能结果: $max_result GFlops" | tee -a "$LOG_FILE"
    fi
fi

# 如果没有找到标准格式，尝试其他模式
if [ -z "$max_result" ]; then
    echo "[INFO] 尝试其他格式提取HPL结果..." | tee -a "$LOG_FILE"

    # 查找包含Gflops的行
    if grep -qi "gflops" "$OUTPUT_DIR/linpack_full.log"; then
        gflops_line=$(grep -i "gflops" "$OUTPUT_DIR/linpack_full.log" | tail -1)
        if [ -n "$gflops_line" ]; then
            # 提取数值（支持科学计数法）
            max_result=$(echo "$gflops_line" | grep -oE '[0-9]+\.?[0-9]*([eE][+-]?[0-9]+)?' | tail -1)
            if [ -n "$max_result" ]; then
                # 转换科学计数法
                max_result=$(echo "$max_result" | awk '{printf "%.4f", $1}')
                average_result="$max_result"
            fi
        fi
    fi
fi

# 设置默认值
[ -z "$average_result" ] && average_result="0.0"
[ -z "$max_result" ] && max_result="0.0"

# 保存结果
echo "Average: $average_result" > "$RESULT_LOG"
echo "Max: $max_result" >> "$RESULT_LOG"

# 生成报告
cpu_model=$(lscpu | grep "Model name" | sed 's/Model name: *//g')
cpu_cores=$(nproc)

echo "# Linpack 测试报告" > "$REPORT_MD"
echo "" >> "$REPORT_MD"
echo "## 测试环境" >> "$REPORT_MD"
echo "- **测试时间**: $(date '+%Y-%m-%d %H:%M:%S')" >> "$REPORT_MD"
echo "- **测试平台**: $ARCH" >> "$REPORT_MD"
echo "- **CPU型号**: $cpu_model" >> "$REPORT_MD"
echo "- **CPU核心数**: $cpu_cores" >> "$REPORT_MD"
if [[ "$ARCH" == "aarch64" || "$ARCH" == "arm64" ]]; then
    echo "- **测试工具**: HPL (ARM编译版)" >> "$REPORT_MD"
else
    echo "- **测试工具**: HPL (AMD优化版)" >> "$REPORT_MD"
fi
echo "" >> "$REPORT_MD"
echo "## 测试结果" >> "$REPORT_MD"
echo "- **平均性能**: $average_result GFlops" >> "$REPORT_MD"
echo "- **最大性能**: $max_result GFlops" >> "$REPORT_MD"

echo "[INFO] Linpack测试完成，结果: $max_result GFlops" | tee -a "$LOG_FILE"
