#!/bin/bash
#==================================
#Author：胡泽志(<EMAIL>)
#V1.3 修改工具路径到/opt/，结果输出到/home/<USER>/sysbench，增加MD报告
#=============================================

# 显示帮助信息
function show_help() {
    cat << 'EOF'
=============================================================================
                    SysBench MySQL 存储性能压测脚本 V1.3
=============================================================================

功能描述：
    本脚本用于对存储设备进行MySQL数据库性能压测，支持NVMe SSD、SATA SSD和HDD
    自动检测存储类型并调整测试参数，生成详细的性能报告和图表

使用方法：
    ./SysbenchPressTest.sh <设备名>,<容量GB>,<厂家型号>
    ./SysbenchPressTest.sh -h|--help

参数说明：
    设备名      - 目标存储设备名称（如：nvme0n1, sda, sdb等）
    容量GB      - 设备容量，单位GB（如：1024, 2048等）
    厂家型号    - 设备厂家和型号信息（如：IntelP5510, SamsungEVO等）

使用示例：
    # 测试Intel P5510 NVMe SSD，容量7680GB
    ./SysbenchPressTest.sh nvme0n1,7680,IntelP5510
    
    # 测试Samsung EVO SATA SSD，容量1024GB
    ./SysbenchPressTest.sh sda,1024,SamsungEVO
    
    # 测试Western Digital HDD，容量4096GB
    ./SysbenchPressTest.sh sdb,4096,WDHDD

测试内容：
    - oltp_read_only      (只读测试)
    - oltp_write_only     (只写测试)
    - oltp_read_write     (读写混合测试)
    - oltp_insert         (插入测试)
    - oltp_update_index   (索引更新测试)
    - oltp_update_non_index (非索引更新测试)

存储类型自动适配：
    NVMe SSD: 高并发(1-128线程)，大数据集(300表x1000万记录)，测试时间15分钟
    SATA SSD: 中并发(1-64线程)，中数据集(200表x800万记录)，测试时间20分钟
    HDD:      低并发(1-16线程)，小数据集(100表x500万记录)，测试时间30分钟

输出结果：
    - 测试报告：/home/<USER>/sysbench/${厂家型号}_${容量}GB.tar
    - 汇总数据：/home/<USER>/sysbench/summary_${厂家型号}_${容量}.csv
    - MD报告：/home/<USER>/sysbench/${厂家型号}_${容量}GB_report.md
    - 性能图表：/home/<USER>/sysbench/${主机名}_${设备名}_performance_comparison.png
    - 详细日志：完整的测试过程日志

注意事项：
    1. 脚本会自动检测并修复Docker环境中的yum源问题
    2. 测试完成后会自动强制卸载目标设备
    3. 测试过程中会占用大量系统资源，建议在专用测试环境运行
    4. 完整测试时间约8-12小时，请确保系统稳定运行
    5. 确保目标设备有足够空间存储测试数据

环境要求：
    - CentOS 7+ 或兼容系统
    - 至少32GB内存（推荐64GB+）
    - gnuplot绘图工具
    - MySQL相关开发包
    - 目标存储设备可用空间 > 数据集大小

=============================================================================
EOF
}
domain="https://saturn.sankuai.com"
# 检查并修复yum功能（参考SpecJBB.sh方式）
function fix_yum_in_docker() {
    echo "[INFO] 检查yum是否可用..."
    
    # 测试yum是否能正常安装软件包
    if ! yum list available wget >/dev/null 2>&1; then
        echo "[INFO] yum源有问题，开始修复..."
        
        # 强制重置yum源
        echo "[INFO] 重置yum源..."
        rm -rf /etc/yum.repos.d/*
        
        arch=$(uname -m)
        if [[ "$arch" == "aarch64" ]]; then
            # ARM平台
            echo "[INFO] 检测到 ARM 架构，使用本地mtos-arm.repo"
            curl -o /etc/yum.repos.d/mtos-arm.repo http://10.8.104.100/huzz/DOCKER/tools/mtos-arm.repo
        else
            # x86平台
            echo "[INFO] 检测到 x86 架构，使用阿里云CentOS 7源"
            curl -o /etc/yum.repos.d/CentOS-Base.repo http://10.8.104.100/huzz/DOCKER/tools/Centos-7.repo
        fi
        
        # 清理并重建yum缓存
        yum clean all 2>/dev/null || true
        yum makecache fast 2>/dev/null || yum makecache 2>/dev/null || true
        
        # 再次测试yum
        if yum list available wget >/dev/null 2>&1; then
            echo "[INFO] yum修复成功"
        else
            echo "[WARNING] yum修复失败，将尝试继续执行"
        fi
    else
        echo "[INFO] yum工作正常"
    fi
    
    # 安装基础工具和MySQL依赖
    echo "[INFO] 安装基础网络工具和MySQL依赖..."
    yum install -y wget curl which numactl numactl-devel sysstat|| echo "[WARNING] 基础工具安装失败"
}

# 检测存储设备类型的函数
function detect_storage_type() {
    local device=$1
    local device_path="/dev/${device}"
    
    # 检查设备是否存在
    if [ ! -b "$device_path" ]; then
        echo "ERROR: Device $device_path does not exist"
        exit 1
    fi
    
    # 检测是否为NVMe设备
    if [[ "$device" =~ ^nvme[0-9]+n[0-9]+$ ]]; then
        echo "NVME"
        return
    fi
    
    # 检测是否为SSD (通过rotational参数)
    local sys_path="/sys/block/${device}/queue/rotational"
    if [ -f "$sys_path" ]; then
        local rotational=$(cat "$sys_path")
        if [ "$rotational" = "0" ]; then
            echo "SSD"
        else
            echo "HDD"
        fi
    else
        # 如果无法检测，默认认为是HDD（保守策略）
        echo "HDD"
    fi
}

# 强制卸载函数
function force_unmount() {
    local device=$1
    local mount_point="/data/${device}"
    
    echo "[INFO] 检查设备 ${device} 的挂载状态..."
    
    # 检查是否已挂载
    if mount | grep -q "${mount_point}"; then
        echo "[INFO] 设备 ${device} 仍在挂载点 ${mount_point}，开始卸载..."
        
        # 尝试正常卸载
        umount "${mount_point}" 2>/dev/null
        sleep 2
        
        # 检查是否卸载成功
        if mount | grep -q "${mount_point}"; then
            echo "[INFO] 正常卸载失败，尝试强制卸载..."
            
            # 杀死使用该挂载点的进程
            fuser -km "${mount_point}" 2>/dev/null
            sleep 2
            
            # 强制卸载
            umount -f "${mount_point}" 2>/dev/null
            sleep 2
            
            # 最后尝试lazy卸载
            if mount | grep -q "${mount_point}"; then
                echo "[INFO] 强制卸载失败，尝试lazy卸载..."
                umount -l "${mount_point}" 2>/dev/null
                sleep 2
            fi
        fi
        
        # 最终检查
        if mount | grep -q "${mount_point}"; then
            echo "[WARNING] 设备 ${device} 仍然挂载在 ${mount_point}，请手动处理"
            return 1
        else
            echo "[INFO] 设备 ${device} 已成功卸载"
            return 0
        fi
    else
        echo "[INFO] 设备 ${device} 未挂载或已卸载"
        return 0
    fi
}

# 检查参数
if [[ $# -eq 0 ]] || [[ "$1" == "-h" ]] || [[ "$1" == "--help" ]]; then
    show_help
    exit 0
fi

# 验证参数格式
if [[ ! "$1" =~ ^[^,]+,[0-9]+,[^,]+$ ]]; then
    echo "[ERROR] 参数格式错误！"
    echo "[ERROR] 正确格式：设备名,容量GB,厂家型号"
    echo "[ERROR] 示例：nvme0n1,7680,IntelP5510"
    echo ""
    echo "使用 -h 或 --help 查看详细帮助信息"
    exit 1
fi

function Init()
{
    # 首先检查并修复yum（参考SpecJBB.sh方式）
    fix_yum_in_docker
    
    SysBenchTar="sysbenchPressTestV0.6"
    
    # 安装gnuplot（用于绘图）
    if ! command -v gnuplot >/dev/null 2>&1; then
        echo "[INFO] 安装gnuplot..."
        yum install gnuplot -y || echo "[WARNING] gnuplot安装失败，图表生成功能将不可用"
    else
        echo "[INFO] gnuplot已安装"
    fi
    
    # 安装必要的系统工具
    echo "[INFO] 安装必要的系统工具..."
    yum install sudo e2fsprogs -y || echo "[WARNING] sudo/e2fsprogs安装失败"
    echo "[INFO] 安装必要的开发工具和依赖..."
    yum install ncurses-devel make pkgconfig automake libtool libsysfs libaio-devel mariadb-devel openssl-devel postgresql-devel unzip -y || echo "[WARNING] 部分依赖安装失败"
    
    # 安装blktrace
    if yum list installed 2>/dev/null | grep -q blktrace; then
        echo "[INFO] blktrace already installed"
    else
        echo "[INFO] 安装blktrace..."
        yum install blktrace -y || echo "[WARNING] blktrace安装失败"
    fi
    
    # 清空/home/<USER>
    rm -rf /home/<USER>/*
    
    # 创建输出目录
    mkdir -p /home/<USER>/sysbench
    # 安装MySQL工具到/opt目录
    if [ ! -d /opt/mysql-5.7.26 ]; then
        mkdir -p /opt
        cd /opt
        echo "[INFO] 下载MySQL工具包到/opt目录..."
        wget http://************/huzz/tool/mysql-5.7.26.tar || {
            echo "[WARNING] MySQL工具包下载失败，尝试备用地址..."
            curl -O http://************/huzz/tool/mysql-5.7.26.tar || echo "[ERROR] MySQL工具包下载失败"
        }
        if [ -f mysql-5.7.26.tar ]; then
            tar xvf mysql-5.7.26.tar
            rm -f mysql-5.7.26.tar
        else
            echo "[ERROR] MySQL工具包不存在，请检查下载"
        fi
        cd -
    fi
    
    # 下载测试工具到/opt目录
    if [ ! -d /opt/${SysBenchTar} ]; then
        cd /opt
        echo "[INFO] 下载SysBench测试工具到/opt目录..."
        
        # 尝试多个下载方式
        if ! wget "${domain}/saturn/testTool?toolName=${SysBenchTar}.zip" -O ${SysBenchTar}.zip; then
            echo "[WARNING] 第一种方式下载失败，尝试直接下载..."
            if ! wget "http://************/huzz/tool/${SysBenchTar}.zip" -O ${SysBenchTar}.zip; then
                echo "[ERROR] 测试工具下载失败，请检查网络连接"
                exit 1
            fi
        fi
        
        # 检查下载的文件是否有效
        if [ -f ${SysBenchTar}.zip ] && [ -s ${SysBenchTar}.zip ]; then
            echo "[INFO] 解压测试工具..."
            unzip -o ${SysBenchTar}.zip || {
                echo "[ERROR] 解压失败，文件可能损坏"
                rm -f ${SysBenchTar}.zip
                exit 1
            }
            rm -f ${SysBenchTar}.zip
        else
            echo "[ERROR] 下载的文件无效或为空"
            exit 1
        fi
        cd -
    fi
    
    # 检查关键文件是否存在
    if [ -d /opt/${SysBenchTar}/mysql ]; then
        chmod 644 /opt/${SysBenchTar}/mysql/run/* 2>/dev/null || echo "[WARNING] 设置权限失败"
        cd /opt/${SysBenchTar}/mysql
        rm -rf /opt/${SysBenchTar}/mysql/test_output/*
        
        # 检查关键脚本是否存在
        if [ ! -f "./run-cases.sh" ]; then
            echo "[ERROR] 关键脚本 run-cases.sh 不存在"
            ls -la ./
            exit 1
        fi
        
        # 修复脚本中的sudo问题
        echo "[INFO] 修复测试脚本中的sudo问题..."
        find . -name "*.sh" -type f -exec sed -i 's/sudo //g' {} \; 2>/dev/null || true
        
        # 修复mke2fs命令，添加-F参数强制格式化
        find . -name "*.sh" -type f -exec sed -i 's/mke2fs /mke2fs -F /g' {} \; 2>/dev/null || true
        find . -name "*.sh" -type f -exec sed -i 's/mkfs.ext4 /mkfs.ext4 -F /g' {} \; 2>/dev/null || true
        
        # 修复可能的交互式命令
        find . -name "*.sh" -type f -exec sed -i 's/read -p/#read -p/g' {} \; 2>/dev/null || true
        find . -name "*.sh" -type f -exec sed -i 's/read /#read /g' {} \; 2>/dev/null || true

        # 修复日志文件路径问题
        find . -name "*.sh" -type f -exec sed -i 's|/var/log/sfx_messages|/tmp/sfx_messages|g' {} \; 2>/dev/null || true
        
        # 创建缺失的日志文件
        touch /tmp/sfx_messages 2>/dev/null || true
        touch /var/log/sfx_messages 2>/dev/null || true

        # 预创建MySQL数据目录
        echo "[INFO] 预创建MySQL数据目录..."
        mkdir -p "/data/${dev_name}/mysql-5.7.26" 2>/dev/null || {
            echo "[WARNING] 无法创建数据目录，将在运行时创建"
        }
        
    else
        echo "[ERROR] SysBench工具目录不存在: /opt/${SysBenchTar}/mysql"
        echo "[INFO] 当前/opt目录内容:"
        ls -la /opt/
        exit 1
    fi
}

# 在Init之前先解析参数，避免参数为空的问题
str=$1
arr=($(echo $str | tr ',' ' '))  

# 验证解析后的参数
if [ ${#arr[@]} -ne 3 ] || [ -z "${arr[0]}" ] || [ -z "${arr[1]}" ] || [ -z "${arr[2]}" ]; then
    echo "[ERROR] 参数解析失败或参数不完整"
    echo "[ERROR] 解析结果: 设备名='${arr[0]}', 容量='${arr[1]}', 厂家型号='${arr[2]}'"
    echo "[ERROR] 请检查参数格式是否正确"
    exit 1
fi

echo "[INFO] 解析参数："
echo "[INFO] 设备名: ${arr[0]}"
echo "[INFO] 容量: ${arr[1]}GB"
echo "[INFO] 厂家型号: ${arr[2]}"

# 预处理设备 - 在Init之前进行设备准备
echo "[INFO] 准备测试设备..."
device_path="/dev/${arr[0]}"

# 强制卸载设备（如果已挂载）
umount "$device_path" 2>/dev/null || true
umount "/data/${arr[0]}" 2>/dev/null || true
# 等待一下确保卸载完成
sleep 2

# 检测存储类型并调整参数
storage_type=$(detect_storage_type "${arr[0]}")
if [[ "$storage_type" == "ERROR:"* ]]; then
    echo "[ERROR] 存储设备检测失败: $storage_type"
    exit 1
fi
echo "[INFO] 检测到存储类型: ${storage_type}"

if [ -b "$device_path" ]; then
    echo "[INFO] 设备 $device_path 存在，开始准备..."
    
    # 强制卸载设备（如果已挂载）
    umount "$device_path" 2>/dev/null || true
    umount "/data/${arr[0]}" 2>/dev/null || true
    
    # 创建挂载点
    mkdir -p "/data/${arr[0]}"
    
    # 检查设备是否有文件系统，如果没有则创建
    if ! blkid "$device_path" >/dev/null 2>&1; then
        echo "[INFO] 设备没有文件系统，创建ext4文件系统（自动模式）..."
        mke2fs -t ext4 -F "$device_path"
    else
        echo "[INFO] 设备已有文件系统"
    fi
    
    # 挂载设备
    if ! mountpoint -q "/data/${arr[0]}"; then
        echo "[INFO] 挂载设备到 /data/${arr[0]}..."
        mount "$device_path" "/data/${arr[0]}" 2>/dev/null || {
            echo "[WARNING] 挂载失败，将使用本地目录进行测试"
        }
    fi
    
    # 确保MySQL目录存在且有正确权限
    mkdir -p "/data/${arr[0]}/mysql-5.7.26"
    chmod 755 "/data/${arr[0]}/mysql-5.7.26"
    chown -R $(whoami) "/data/${arr[0]}/mysql-5.7.26" 2>/dev/null || true
    
    echo "[INFO] 设备准备完成"
else
    echo "[WARNING] 设备 $device_path 不存在，将使用本地目录进行测试"
    mkdir -p "/data/${arr[0]}/mysql-5.7.26"
fi

Init;

export formal_test="formal"
export dev_name=${arr[0]}
export capacity_gb=${arr[1]}
export prep_dev=yes
export init_db=yes
export atomic_write=0
export WORKLOADS=sb/mysql-5.7

# 检测存储类型并调整参数
echo "[INFO] 检测到存储类型: ${storage_type}"

if [[ "${formal_test}" == "" ]] 
then
    export workload_set="prepare oltp_read_write"
    export warmup_time=5
    export run_time=30
    export thread_count_list="1 4"
    export table_count=32
    export table_size=200000
else
    export workload_set="prepare oltp_read_only oltp_insert oltp_update_index oltp_update_non_index oltp_read_write oltp_write_only"
    
    # 根据存储类型调整测试参数
    case ${storage_type} in
        "NVME")
            echo "[INFO] 配置NVMe SSD测试参数（高性能）"
            export run_time=900
            export thread_count_list="1 2 4 8 16 32 64 128"
            export table_count=300
            export table_size=10000000
            export innodb_buffer_pool_size=32G
            ;;
        "SSD")
            echo "[INFO] 配置SATA SSD测试参数（中等性能）"
            export run_time=1200
            export thread_count_list="1 2 4 8 16 32 64"
            export table_count=200
            export table_size=8000000
            export innodb_buffer_pool_size=24G
            ;;
        "HDD")
            echo "[INFO] 配置HDD测试参数（低并发，长时间）"
            export run_time=1800
            export thread_count_list="1 2 4 8 16"
            export table_count=100
            export table_size=5000000
            export innodb_buffer_pool_size=16G
            ;;
        *)
            echo "[INFO] 未知存储类型，使用默认HDD参数"
            export run_time=1800
            export thread_count_list="1 2 4 8 16"
            export table_count=100
            export table_size=5000000
            export innodb_buffer_pool_size=16G
            ;;
    esac
fi

export iostat_dev_str=${dev_name}
export dev_pattern=${iostat_dev_str}
export table_data_src_file=""
export run_cmd_script=./run-cases.sh
export collect_blktrace=0
export blktrace_time=30
export WORKSPACE=./

# 切换到工具目录
cd /opt/sysbenchPressTestV0.6/mysql

pushd ../
if [ ! -d bin ]; then tar xzf bin.tgz; fi
if [ ! -d compress ]; then tar xzf compress.tgz; fi
popd

export cfg_list=${WORKLOADS}
export chart_title="${arr[2]}_mysql-5.7_${storage_type}"
export my_folder=${arr[2]}
source ${run_cmd_script}

# 测试完成后强制卸载检查
echo "[INFO] 测试完成，开始卸载检查..."
force_unmount ${dev_name}

# 输出结果
echo "[INFO] 测试完成，开始整理结果..."
testDate=`date +%Y%m%d_%H%M%S`

# 修复结果目录路径查找
echo "[INFO] 查找结果目录..."

# 首先尝试在当前工作目录下查找
result_dir=$(find ./test_output -name "*${my_folder}*${capacity_gb}GB*" -type d 2>/dev/null | head -1)

# 如果没找到，尝试在/opt/sysbenchPressTestV0.6/mysql/test_output下查找
if [ -z "$result_dir" ]; then
    result_dir=$(find /opt/sysbenchPressTestV0.6/mysql/test_output -name "*${my_folder}*${capacity_gb}GB*" -type d 2>/dev/null | head -1)
    fi

# 如果还没找到，尝试更广泛的搜索
if [ -z "$result_dir" ]; then
    result_dir=$(find /opt -name "*${my_folder}*${capacity_gb}GB*" -type d 2>/dev/null | grep test_output | head -1)
            fi
            
if [ -z "$result_dir" ]; then
    echo "[ERROR] 找不到结果目录，尝试列出可能的目录："
    echo "[INFO] 当前目录下的test_output："
    ls -la ./test_output/ 2>/dev/null || echo "  - 当前目录下没有test_output"
    echo "[INFO] /opt/sysbenchPressTestV0.6/mysql/test_output下的内容："
    ls -la /opt/sysbenchPressTestV0.6/mysql/test_output/ 2>/dev/null || echo "  - 该路径不存在"
    exit 1
        fi

echo "[INFO] 找到结果目录: $result_dir"

# 进入结果目录进行数据处理
cd "$result_dir" || {
    echo "[ERROR] 无法进入结果目录: $result_dir"
    exit 1
}

echo "[INFO] 当前工作目录: $(pwd)"

# 检查汇总文件是否存在
summary_file="summary_${my_folder}_${capacity_gb}.csv"
if [ ! -f "$summary_file" ]; then
    echo "[ERROR] 汇总文件不存在: $summary_file"
    echo "[INFO] 当前目录下的CSV文件："
    ls -la *.csv 2>/dev/null || echo "  - 没有找到CSV文件"
    exit 1
        fi

echo "[INFO] 找到汇总文件: $summary_file"
echo "[INFO] 开始处理数据并生成图表..."

# 检查gnuplot是否可用
if ! command -v gnuplot >/dev/null 2>&1; then
    echo "[WARNING] gnuplot不可用，跳过图表生成"
else
    # 获取主机名和设备名，生成图表标题和文件名
    hostname=$(hostname)
    device_name=${dev_name}
    chart_title="${hostname}_${device_name}"
    
    echo "[INFO] 图表标题: $chart_title"
    
    # 清理之前的临时文件
    rm -f oltp_*.csv ${chart_title}_*.png *.plt
    
    # 生成各类 workload 的 threads-TPS 数据文件
    echo "[INFO] 提取各workload的TPS数据..."

    awk -F, '
    NR==1 {
        for(i=1;i<=NF;i++) {
            if($i=="thrd cnt") thrd=i; 
            if($i=="workload") wl=i; 
            if($i=="tps") tps=i;
        }
        print "找到列位置: thrd=" thrd ", workload=" wl ", tps=" tps > "/dev/stderr"
    }
    NR>1 {
        gsub(/ /, "_", $wl);
        if($wl=="oltp_read_only")        print $thrd " " $tps >> "oltp_read_only.csv";
        if($wl=="oltp_write_only")       print $thrd " " $tps >> "oltp_write_only.csv";
        if($wl=="oltp_read_write")       print $thrd " " $tps >> "oltp_read_write.csv";
        if($wl=="oltp_insert")           print $thrd " " $tps >> "oltp_insert.csv";
        if($wl=="oltp_update_index")     print $thrd " " $tps >> "oltp_update_index.csv";
        if($wl=="oltp_update_non_index") print $thrd " " $tps >> "oltp_update_non_index.csv";
    }
    ' "$summary_file"

    # 检查生成的CSV文件
    echo "[INFO] 生成的数据文件："
    for csv_file in oltp_*.csv; do
        if [ -f "$csv_file" ]; then
            line_count=$(wc -l < "$csv_file")
            echo "  - $csv_file ($line_count 行数据)"
        fi
    done

    # 只生成综合对比图表
    echo "[INFO] 生成综合性能对比图表..."

    cat > performance_plot.plt << EOF
# 设置输出格式和文件
set terminal png size 1200,800
set output "${chart_title}_performance_comparison.png"

# 设置图表标题和标签
set title "MySQL SysBench Performance Comparison - ${chart_title}"
set xlabel "Threads"
set ylabel "TPS (Transactions Per Second)"

# 设置网格
set grid

# 设置图例
set key top left

# 设置线条样式
set style line 1 lc rgb '#e41a1c' lt 1 lw 2 pt 7 ps 1.2
set style line 2 lc rgb '#377eb8' lt 1 lw 2 pt 5 ps 1.2
set style line 3 lc rgb '#4daf4a' lt 1 lw 2 pt 9 ps 1.2
set style line 4 lc rgb '#984ea3' lt 1 lw 2 pt 11 ps 1.2
set style line 5 lc rgb '#ff7f00' lt 1 lw 2 pt 13 ps 1.2
set style line 6 lc rgb '#ffff33' lt 1 lw 2 pt 15 ps 1.2

# 绘制多条曲线
plot "oltp_insert.csv" using 1:2 with linespoints ls 1 title "Insert Test", \\
     "oltp_read_only.csv" using 1:2 with linespoints ls 2 title "Read Only Test", \\
     "oltp_read_write.csv" using 1:2 with linespoints ls 3 title "Read Write Test", \\
     "oltp_update_index.csv" using 1:2 with linespoints ls 4 title "Update Index Test", \\
     "oltp_update_non_index.csv" using 1:2 with linespoints ls 5 title "Update Non-Index Test", \\
     "oltp_write_only.csv" using 1:2 with linespoints ls 6 title "Write Only Test"
EOF

    # 执行gnuplot绘图
    echo "[INFO] 执行gnuplot绘图..."
    if gnuplot performance_plot.plt 2>/dev/null; then
        echo "[INFO] 综合性能图表生成成功: ${chart_title}_performance_comparison.png"
    else
        echo "[WARNING] gnuplot绘图失败，但继续执行"
        fi

    # 清理临时文件
    rm -f performance_plot.plt
    # 生成简单的性能分析报告
    echo ""
    echo "[INFO] ========== 性能分析摘要 =========="

    for csv_file in oltp_*.csv; do
        if [ -f "$csv_file" ]; then
            workload_name=$(basename "$csv_file" .csv)
            max_tps=$(awk 'BEGIN{max=0} {if($2>max) max=$2} END{print max}' "$csv_file")
            best_thread=$(awk -v max_tps="$max_tps" '$2==max_tps {print $1; exit}' "$csv_file")
            
            case $workload_name in
                "oltp_read_only") desc="只读测试" ;;
                "oltp_write_only") desc="只写测试" ;;
                "oltp_read_write") desc="读写混合" ;;
                "oltp_insert") desc="插入测试" ;;
                "oltp_update_index") desc="索引更新" ;;
                "oltp_update_non_index") desc="非索引更新" ;;
                *) desc="未知测试" ;;
            esac
            
            printf "%-20s: 最高TPS=%-8.2f (线程数=%s)\n" "$desc" "$max_tps" "$best_thread"
        fi
    done

    echo "[INFO] ========================================="
fi

# 复制结果文件到输出目录
echo "[INFO] 复制结果文件到 /home/<USER>/sysbench..."

# 复制CSV文件
if [ -f "$summary_file" ]; then
    cp "$summary_file" "/home/<USER>/sysbench/"
    echo "[INFO] 复制汇总文件: $summary_file"
fi

# 复制图表文件
if [ -f "${chart_title}_performance_comparison.png" ]; then
    cp "${chart_title}_performance_comparison.png" "/home/<USER>/sysbench/"
    echo "[INFO] 复制图表文件: ${chart_title}_performance_comparison.png"
fi

# 复制tar包（如果存在）
tar_file="${my_folder}_${capacity_gb}GB.tar"
if [ -f "$tar_file" ]; then
    cp "$tar_file" "/home/<USER>/sysbench/"
    echo "[INFO] 复制测试结果包: $tar_file"
fi

# 生成MD格式报告
echo "[INFO] 生成MD格式报告..."

report_file="/home/<USER>/sysbench/${my_folder}_${capacity_gb}GB_report.md"

cat > "$report_file" << EOF
# MySQL SysBench 性能测试报告

## 测试概要

- **测试时间**: $(date '+%Y-%m-%d %H:%M:%S')
- **测试主机**: $(hostname)
- **测试设备**: ${dev_name}
- **设备容量**: ${capacity_gb}GB
- **厂家型号**: ${my_folder}
- **存储类型**: ${storage_type}

## 测试配置

### 存储设备信息
- 设备路径: /dev/${dev_name}
- 存储类型: ${storage_type}
- 容量: ${capacity_gb}GB

### 测试参数
EOF

# 根据存储类型添加测试参数
case ${storage_type} in
    "NVME")
        cat >> "$report_file" << EOF
- 测试时间: 15分钟/workload
- 并发线程: 1, 2, 4, 8, 16, 32, 64, 128
- 表数量: 300
- 每表记录数: 1000万
- InnoDB缓冲池: 32GB
EOF
        ;;
    "SSD")
        cat >> "$report_file" << EOF
- 测试时间: 20分钟/workload
- 并发线程: 1, 2, 4, 8, 16, 32, 64
- 表数量: 200
- 每表记录数: 800万
- InnoDB缓冲池: 24GB
EOF
        ;;
    "HDD")
        cat >> "$report_file" << EOF
- 测试时间: 30分钟/workload
- 并发线程: 1, 2, 4, 8, 16
- 表数量: 100
- 每表记录数: 500万
- InnoDB缓冲池: 16GB
EOF
        ;;
esac

cat >> "$report_file" << EOF

### 测试场景
- oltp_read_only: 只读测试
- oltp_write_only: 只写测试
- oltp_read_write: 读写混合测试
- oltp_insert: 插入测试
- oltp_update_index: 索引更新测试
- oltp_update_non_index: 非索引更新测试

## 性能测试结果

### 性能曲线图

![性能对比图](/home/<USER>/sysbench/${chart_title}_performance_comparison.png)

### 性能数据摘要

| 测试场景 | 最高TPS | 最佳线程数 |
|---------|---------|-----------|
EOF

# 添加性能数据到MD报告
for csv_file in oltp_*.csv; do
    if [ -f "$csv_file" ]; then
        workload_name=$(basename "$csv_file" .csv)
        max_tps=$(awk 'BEGIN{max=0} {if($2>max) max=$2} END{print max}' "$csv_file")
        best_thread=$(awk -v max_tps="$max_tps" '$2==max_tps {print $1; exit}' "$csv_file")
        
        case $workload_name in
            "oltp_read_only") desc="只读测试" ;;
            "oltp_write_only") desc="只写测试" ;;
            "oltp_read_write") desc="读写混合" ;;
            "oltp_insert") desc="插入测试" ;;
            "oltp_update_index") desc="索引更新" ;;
            "oltp_update_non_index") desc="非索引更新" ;;
            *) desc="未知测试" ;;
        esac
        
        echo "| $desc | $max_tps | $best_thread |" >> "$report_file"
    fi
done

cat >> "$report_file" << EOF

## 详细测试数据

详细的测试数据请参考: [summary_${my_folder}_${capacity_gb}.csv](/home/<USER>/sysbench/summary_${my_folder}_${capacity_gb}.csv)

## 测试结论

根据测试结果分析：

1. **最佳性能场景**: 
EOF

# 找出最高TPS的测试场景
max_overall_tps=0
best_workload=""
for csv_file in oltp_*.csv; do
    if [ -f "$csv_file" ]; then
        workload_name=$(basename "$csv_file" .csv)
        max_tps=$(awk 'BEGIN{max=0} {if($2>max) max=$2} END{print max}' "$csv_file")
        if (( $(echo "$max_tps > $max_overall_tps" | bc -l) )); then
            max_overall_tps=$max_tps
            best_workload=$workload_name
        fi
    fi
done

case $best_workload in
    "oltp_read_only") best_desc="只读测试" ;;
    "oltp_write_only") best_desc="只写测试" ;;
    "oltp_read_write") best_desc="读写混合" ;;
    "oltp_insert") best_desc="插入测试" ;;
    "oltp_update_index") best_desc="索引更新" ;;
    "oltp_update_non_index") best_desc="非索引更新" ;;
    *) best_desc="未知测试" ;;
esac

cat >> "$report_file" << EOF
   - ${best_desc}场景下获得最高TPS: ${max_overall_tps}

2. **存储类型特征**: ${storage_type}类型存储设备表现符合预期

3. **并发性能**: 不同workload在不同并发度下的表现各有特点，详见性能曲线图

## 文件说明

- **测试报告**: ${my_folder}_${capacity_gb}GB_report.md (本文件)
- **汇总数据**: summary_${my_folder}_${capacity_gb}.csv
- **性能图表**: ${chart_title}_performance_comparison.png
- **完整结果**: ${my_folder}_${capacity_gb}GB.tar

---
*报告生成时间: $(date '+%Y-%m-%d %H:%M:%S')*
*测试工具: SysBench MySQL 存储性能压测脚本 V1.3*
EOF
echo "[INFO] MD报告生成完成: $report_file"

# 显示最终结果
echo ""
echo "[INFO] ========== 测试完成 =========="
echo "[INFO] 所有结果文件已保存到: /home/<USER>/sysbench/"
echo "[INFO] 主要文件："
echo "[INFO]   - 测试报告: ${my_folder}_${capacity_gb}GB_report.md"
echo "[INFO]   - 汇总数据: summary_${my_folder}_${capacity_gb}.csv"
echo "[INFO]   - 性能图表: ${chart_title}_performance_comparison.png"
echo "[INFO]   - 完整结果: ${my_folder}_${capacity_gb}GB.tar"
echo "[INFO] 脚本执行完成，存储类型: ${storage_type}"
