#!/bin/bash
#set -e

# 平台识别
arch=$(uname -m)

# 检测是否在容器中运行
if [ -f /.dockerenv ] || grep -q 'docker\|lxc' /proc/1/cgroup 2>/dev/null; then
    echo "[INFO] 检测到容器环境"
    IN_CONTAINER=true
else
    echo "[INFO] 物理机/虚拟机环境"
    IN_CONTAINER=false
fi

# 强制重置yum源
echo "[INFO] 重置yum源..."
rm -rf /etc/yum.repos.d/*

if [[ "$arch" == "aarch64" ]]; then
    # ARM平台
    echo "[INFO] 检测到 ARM 架构，使用本地mtos-arm.repo"
    curl -o /etc/yum.repos.d/mtos-arm.repo http://10.8.104.100/huzz/DOCKER/tools/mtos-arm.repo
else
    # x86平台
    echo "[INFO] 检测到 x86 架构，使用阿里云CentOS 7源"
    curl -o /etc/yum.repos.d/CentOS-Base.repo  http://10.8.104.100/huzz/DOCKER/tools/Centos-7.repo
fi

yum clean all
yum makecache

# 容器中跳过系统更新，避免大量依赖冲突
if [ "$IN_CONTAINER" = "true" ]; then
    echo "[INFO] 容器环境，跳过系统更新"
else
    echo "[INFO] 执行系统更新..."
    yum update -y
fi

# 尝试安装 epel-release，如果失败就跳过
echo "[INFO] 尝试安装 epel-release..."
if yum install -y epel-release; then
    echo "[INFO] epel-release 安装成功"
    
    # 检查 epel 源是否可用
    if ! yum repolist epel >/dev/null 2>&1; then
        echo "[WARN] epel 源不可用，禁用 epel 源"
        yum-config-manager --disable epel 2>/dev/null || sed -i 's/enabled=1/enabled=0/' /etc/yum.repos.d/epel.repo 2>/dev/null || true
    fi
else
    echo "[WARN] epel-release 安装失败，继续使用基础源"
fi

# 强制清理并重建缓存
yum clean all
yum makecache fast || yum makecache

# 安装依赖，使用 --skip-broken 和分批安装
echo "[INFO] 安装基础工具包..."

# 第一批：基础工具
BASIC_PACKAGES="wget curl make bzip2 file"
for pkg in $BASIC_PACKAGES; do
    echo "[INFO] 安装 $pkg..."
    if ! yum install -y "$pkg"; then
        echo "[WARN] $pkg 安装失败，尝试从其他源安装"
        yum install -y "$pkg" --disablerepo=epel || echo "[ERROR] $pkg 安装失败"
    fi
done

# 检查 wget 是否安装成功
if ! command -v wget >/dev/null 2>&1; then
    echo "[ERROR] wget 安装失败，尝试替代方案..."
    
    # 尝试使用 curl 替代 wget 的功能
    if command -v curl >/dev/null 2>&1; then
        echo "[INFO] 使用 curl 替代 wget"
        # 创建 wget 的 curl 包装脚本
        cat > /usr/local/bin/wget << 'EOF'
#!/bin/bash
# wget to curl wrapper
if [[ "$1" == "-O" ]]; then
    curl -L -o "$2" "$3"
elif [[ "$1" == "-c" ]]; then
    curl -L -C - -o "$(basename "$2")" "$2"
else
    curl -L -O "$1"
fi
EOF
        chmod +x /usr/local/bin/wget
        export PATH="/usr/local/bin:$PATH"
    else
        echo "[ERROR] 无法安装 wget 或 curl，脚本无法继续"
        exit 1
    fi
fi

# 第二批：编译工具
echo "[INFO] 安装编译工具..."
COMPILE_PACKAGES="gcc gcc-c++ gcc-gfortran glibc-devel"
yum install -y $COMPILE_PACKAGES --skip-broken || {
    echo "[WARN] 批量安装编译工具失败，尝试单独安装..."
    for pkg in $COMPILE_PACKAGES; do
        yum install -y "$pkg" --disablerepo=epel || echo "[WARN] $pkg 安装失败"
    done
}

# 第三批：其他依赖
echo "[INFO] 安装其他依赖..."
OTHER_PACKAGES="m4 numactl automake dmidecode"
yum install -y $OTHER_PACKAGES --skip-broken || {
    echo "[WARN] 批量安装其他工具失败，尝试单独安装..."
    for pkg in $OTHER_PACKAGES; do
        yum install -y "$pkg" --disablerepo=epel || echo "[WARN] $pkg 安装失败"
    done
}

# 尝试安装 jemalloc（可选）
echo "[INFO] 尝试安装 jemalloc..."
yum install -y jemalloc jemalloc-devel --skip-broken || echo "[WARN] jemalloc 安装失败，将跳过"

# 尝试安装 libnsl（可选）
echo "[INFO] 尝试安装 libnsl..."
yum install -y libnsl libnsl2 --skip-broken || echo "[WARN] libnsl 安装失败，将跳过"

# 检查关键命令
echo "[INFO] 检查关键命令..."
missing_commands=()

for cmd in wget make gcc; do
    if ! command -v $cmd >/dev/null 2>&1; then
        missing_commands+=($cmd)
        echo "[ERROR] $cmd 未安装"
    else
        echo "[INFO] $cmd 已安装: $(command -v $cmd)"
    fi
done

# 检查可选命令
for cmd in g++ gfortran; do
    if ! command -v $cmd >/dev/null 2>&1; then
        echo "[WARN] $cmd 未安装（可选）"
    else
        echo "[INFO] $cmd 已安装: $(command -v $cmd)"
    fi
done

# 如果缺少关键命令，尝试最后一次安装
if [ ${#missing_commands[@]} -gt 0 ]; then
    echo "[WARN] 缺少关键命令: ${missing_commands[*]}，尝试最后一次安装..."
    
    for cmd in "${missing_commands[@]}"; do
        case $cmd in
            "wget")
                yum install -y wget --disablerepo=epel || {
                    echo "[ERROR] wget 安装失败，无法继续"
                    exit 1
                }
                ;;
            "make")
                yum install -y make --disablerepo=epel || {
                    echo "[ERROR] make 安装失败，无法继续"
                    exit 1
                }
                ;;
            "gcc")
                yum install -y gcc --disablerepo=epel || {
                    echo "[ERROR] gcc 安装失败，无法继续"
                    exit 1
                }
                ;;
        esac
    done
    
    # 重新检查
    for cmd in "${missing_commands[@]}"; do
        if ! command -v $cmd >/dev/null 2>&1; then
            echo "[ERROR] $cmd 仍然未安装，脚本退出"
            exit 1
        fi
    done
fi

echo "[INFO] 基础依赖检查通过"

# 下载并解压gcc
cd /usr/local/src
echo "download gcc file"
BASE_URL=http://10.8.104.100/huzz/DOCKER/tools

wget -c $BASE_URL/gcc-10.5.0.tar.gz
wget -c $BASE_URL/gmp-6.1.0.tar.bz2
wget -c $BASE_URL/mpfr-3.1.6.tar.bz2
wget -c $BASE_URL/mpc-1.0.3.tar.gz
wget -c $BASE_URL/isl-0.18.tar.bz2

# 解压前清理旧目录
rm -rf /usr/local/src/gcc-10.5.0
tar -xzf gcc-10.5.0.tar.gz
cd gcc-10.5.0

# 解压依赖包
tar xf ../gmp-6.1.0.tar.bz2 && mv gmp-6.1.0 gmp
tar xf ../mpfr-3.1.6.tar.bz2 && mv mpfr-3.1.6 mpfr
tar xf ../mpc-1.0.3.tar.gz && mv mpc-1.0.3 mpc
tar xf ../isl-0.18.tar.bz2 && mv isl-0.18 isl

# 创建build目录（幂等）
mkdir -p build
cd build

echo "Install gcc....please waiting"
../configure --prefix=/usr/local/gcc-10.5.0 --enable-languages=c,c++,fortran --disable-multilib
make -j$(nproc)
make install

# 配置环境变量
echo 'export PATH=/usr/local/gcc-10.5.0/bin:$PATH' > /etc/profile.d/gcc10.sh
echo 'export LD_LIBRARY_PATH=/usr/local/gcc-10.5.0/lib64:$LD_LIBRARY_PATH' >> /etc/profile.d/gcc10.sh
sleep 1s
source /etc/profile.d/gcc10.sh

# 验证
gcc --version
g++ --version
gfortran --version

# 清理源码和压缩包
cd /usr/local/src
rm -rf gcc-10.5.0 gcc-10.5.0.tar.gz

echo "GCC 10.5.0 安装完成并已配置环境变量。"

# ========== 修复 SpecCPU2017 下载和安装部分 ==========
SPECCPU_TAR_URL="http://10.8.104.100/huzz/DOCKER/tools/Speccpu2017.tar"
SPECCPU_TAR="/opt/Speccpu2017.tar"
SPECCPU_DIR="/opt/speccpu2017"

echo "[INFO] 开始处理 SpecCPU2017 安装..."

# 清理旧的安装
if [ -d "$SPECCPU_DIR" ]; then
    echo "[INFO] 清理旧的 SpecCPU2017 安装目录..."
    rm -rf "$SPECCPU_DIR"
fi

# 下载 SpecCPU2017
echo "[INFO] 下载 SpecCPU2017 安装包..."
mkdir -p /opt
rm -f "$SPECCPU_TAR"

# 尝试多种下载方式
if ! curl -f -o "$SPECCPU_TAR" "$SPECCPU_TAR_URL"; then
    echo "[WARN] curl 下载失败，尝试 wget..."
    if ! wget -O "$SPECCPU_TAR" "$SPECCPU_TAR_URL"; then
        echo "[ERROR] 下载 SpecCPU2017 失败，请检查网络和URL！"
        echo "[ERROR] 尝试的URL: $SPECCPU_TAR_URL"
        exit 1
    fi
fi

# 验证下载的文件
echo "[INFO] 验证下载的文件..."
if [ ! -f "$SPECCPU_TAR" ]; then
    echo "[ERROR] 下载的文件不存在！"
    exit 1
fi

file_size=$(stat -c%s "$SPECCPU_TAR" 2>/dev/null || echo "0")
if [ "$file_size" -lt 10485760 ]; then  # 小于10MB认为异常
    echo "[ERROR] 下载的文件过小 ($file_size bytes)，可能下载失败！"
    echo "[ERROR] 文件内容预览："
    head -20 "$SPECCPU_TAR"
    exit 1
fi

echo "[INFO] 文件下载成功，大小: $file_size bytes"

# 检查文件类型
file_type=$(file "$SPECCPU_TAR")
echo "[INFO] 文件类型: $file_type"

# 创建解压目录
mkdir -p "$SPECCPU_DIR"

# 解压文件
echo "[INFO] 解压 SpecCPU2017 到 $SPECCPU_DIR ..."
cd /opt

# 先尝试查看 tar 包结构
echo "[INFO] 查看 tar 包结构..."
tar -tf "$SPECCPU_TAR" | head -10

# 尝试不同的解压方式
if tar -tf "$SPECCPU_TAR" | head -1 | grep -q "^speccpu2017/"; then
    # 如果顶层目录是 speccpu2017/
    echo "[INFO] 检测到顶层目录为 speccpu2017/，使用 strip-components=1"
    tar -xf "$SPECCPU_TAR" -C "$SPECCPU_DIR" --strip-components=1
elif tar -tf "$SPECCPU_TAR" | head -1 | grep -q "^cpu2017/"; then
    # 如果顶层目录是 cpu2017/
    echo "[INFO] 检测到顶层目录为 cpu2017/，使用 strip-components=1"
    tar -xf "$SPECCPU_TAR" -C "$SPECCPU_DIR" --strip-components=1
else
    # 直接解压
    echo "[INFO] 直接解压到目标目录"
    tar -xf "$SPECCPU_TAR" -C "$SPECCPU_DIR"
fi

# 检查解压结果
if [ ! -d "$SPECCPU_DIR" ] || [ ! "$(ls -A $SPECCPU_DIR)" ]; then
    echo "[ERROR] 解压失败或目录为空！"
    echo "[ERROR] 目录内容："
    ls -la "$SPECCPU_DIR"
    exit 1
fi

echo "[INFO] 解压完成，目录内容："
ls -la "$SPECCPU_DIR" | head -10

# 查找关键文件
echo "[INFO] 查找关键文件..."
find "$SPECCPU_DIR" -name "shrc" -o -name "install.sh" | head -5

# 如果文件在子目录中，需要调整
if [ ! -f "$SPECCPU_DIR/shrc" ] && [ ! -f "$SPECCPU_DIR/install.sh" ]; then
    echo "[INFO] 在顶层目录未找到关键文件，查找子目录..."
    
    # 查找可能的 SpecCPU 目录
    spec_subdir=$(find "$SPECCPU_DIR" -name "shrc" -exec dirname {} \; | head -1)
    if [ -n "$spec_subdir" ]; then
        echo "[INFO] 找到 SpecCPU 文件在: $spec_subdir"
        echo "[INFO] 移动文件到正确位置..."
        
        # 创建临时目录
        temp_dir="/opt/speccpu_temp"
        mv "$spec_subdir" "$temp_dir"
        rm -rf "$SPECCPU_DIR"
        mv "$temp_dir" "$SPECCPU_DIR"
    fi
fi

# 最终验证
if [ ! -f "$SPECCPU_DIR/shrc" ]; then
    echo "[ERROR] 未找到 $SPECCPU_DIR/shrc 文件！"
    echo "[ERROR] 当前目录结构："
    find "$SPECCPU_DIR" -type f -name "*shrc*" -o -name "*install*" | head -10
    exit 1
fi

echo "[INFO] SpecCPU2017 解压成功"

# 清理下载的 tar 包
rm -f "$SPECCPU_TAR"

# ========== 下载配置文件 ==========
CONFIG_NAME="cpu2017-gcc10.5-x86.cfg"
CONFIG_PATH="$SPECCPU_DIR/config/$CONFIG_NAME"

if [ ! -f "$CONFIG_PATH" ]; then
    echo "[INFO] 下载配置文件 $CONFIG_NAME ..."
    mkdir -p "$SPECCPU_DIR/config"
    
    CONFIG_URL="http://10.8.104.100/huzz/DOCKER/SpecCPUcfg/cpu2017-gcc10.5-x86.cfg"
    if ! wget -O "$CONFIG_PATH" "$CONFIG_URL"; then
        echo "[ERROR] 下载配置文件失败！"
        exit 1
    fi
    echo "[INFO] 配置文件下载完成"
fi

# ========== 执行安装 ==========
if [ -f "$SPECCPU_DIR/install.sh" ]; then
    echo "[INFO] 执行 SpecCPU2017 安装脚本..."
    cd "$SPECCPU_DIR"
    
    # 使用 expect 或者 here document 自动回答安装问题
    bash install.sh <<EOF
$SPECCPU_DIR
yes
EOF
    
    if [ $? -eq 0 ]; then
        echo "[INFO] SpecCPU2017 安装完成"
    else
        echo "[ERROR] SpecCPU2017 安装失败"
        exit 1
    fi
else
    echo "[ERROR] 未找到安装脚本 $SPECCPU_DIR/install.sh"
    exit 1
fi

# 最终验证安装
if [ -f "$SPECCPU_DIR/shrc" ] && [ -d "$SPECCPU_DIR/config" ]; then
    echo "[INFO] SpecCPU2017 安装验证成功"
else
    echo "[ERROR] SpecCPU2017 安装验证失败"
    exit 1
fi