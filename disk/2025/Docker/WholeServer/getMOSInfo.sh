#!/bin/bash

# 检查参数
if [ $# -ne 1 ]; then
    echo "使用方法: $0 <SN号>"
    echo "示例: $0 219628192799"
    exit 1
fi

SN=$1

echo "正在查询SN: $SN 的机器信息..."

# 获取机器基本信息
MACHINE_INFO=$(bmscli baremetal-list --new --search $SN -d 2>/dev/null)
if [ $? -ne 0 ] || [ -z "$MACHINE_INFO" ]; then
    echo "错误: 无法找到SN为 $SN 的机器"
    exit 1
fi

# 提取机器名称
MACHINE_NAME=$(echo "$MACHINE_INFO" | grep -v "^+" | grep -v "^|.*Id.*|" | grep "|" | awk -F'|' '{print $3}' | tr -d ' ')
if [ -z "$MACHINE_NAME" ]; then
    echo "错误: 无法获取机器名称"
    exit 1
fi

echo "找到机器: $MACHINE_NAME"

# 获取登录凭据
echo "正在获取登录凭据..."
METADATA=$(bmscli baremetal-metadata $MACHINE_NAME 2>/dev/null)
if [ $? -ne 0 ]; then
    echo "错误: 无法获取机器 $MACHINE_NAME 的元数据"
    exit 1
fi

# 提取IP、用户名和密码
IP=$(echo "$METADATA" | grep "ip" | awk -F'|' '{print $3}' | tr -d ' ')
USERNAME=$(echo "$METADATA" | grep "username" | awk -F'|' '{print $3}' | tr -d ' ')
PASSWORD=$(echo "$METADATA" | grep "password" | awk -F'|' '{print $3}' | tr -d ' ')

if [ -z "$IP" ] || [ -z "$USERNAME" ] || [ -z "$PASSWORD" ]; then
    echo "错误: 无法获取完整的登录信息"
    exit 1
fi

echo "登录信息获取成功"
echo "正在收集硬件信息..."

# 创建临时脚本文件用于远程执行
TEMP_SCRIPT=$(mktemp)
cat > $TEMP_SCRIPT << 'EOF'
#!/bin/bash

echo "=========================================="
echo "硬件配置摘要"
echo "=========================================="

# CPU信息
echo -n "CPU: "
cpu_model=$(lscpu | grep "Model name" | head -1 | sed 's/Model name://g' | sed 's/^[ \t]*//' | sed 's/[ \t]*$//')
cpu_sockets=$(lscpu | grep "Socket(s)" | awk '{print $2}')
echo "${cpu_model} *${cpu_sockets}"

# 内存信息
echo -n "Mem: "
if command -v dmidecode >/dev/null 2>&1; then
    # 获取内存条信息
    mem_part=$(dmidecode -t memory | grep "Part Number" | grep -v "Not Specified" | grep -v "NO DIMM" | head -1 | awk -F: '{print $2}' | sed 's/^[ \t]*//' | sed 's/[ \t]*$//')
    mem_size=$(dmidecode -t memory | grep "Size:" | grep -v "No Module Installed" | head -1 | awk '{print $2" "$3}')
    mem_count=$(dmidecode -t memory | grep "Size:" | grep -v "No Module Installed" | wc -l)
    echo "${mem_part}--${mem_size}*${mem_count}"
else
    echo "无法获取详细内存信息"
fi

# 硬盘信息
echo -n "硬盘: "
disk_output=""

# 获取NVME设备
if [ -d /sys/class/nvme ]; then
    nvme_model=""
    nvme_count=0
    for nvme in /sys/class/nvme/nvme*; do
        if [ -d "$nvme" ] && [ -f "$nvme/model" ]; then
            model=$(cat $nvme/model | sed 's/^[ \t]*//' | sed 's/[ \t]*$//')
            if [ -z "$nvme_model" ]; then
                nvme_model="$model"
                nvme_count=1
            elif [ "$nvme_model" = "$model" ]; then
                nvme_count=$((nvme_count + 1))
            fi
        fi
    done
    if [ ! -z "$nvme_model" ]; then
        disk_output="${nvme_model} *${nvme_count}"
    fi
fi

# 获取SATA/SAS设备
sata_devices=$(lsblk -d -o NAME,MODEL | grep -E "^sd[a-z]" | awk '{print $2}' | grep -v "^$")
if [ ! -z "$sata_devices" ]; then
    sata_model=$(echo "$sata_devices" | head -1)
    sata_count=$(echo "$sata_devices" | wc -l)
    if [ ! -z "$disk_output" ]; then
        disk_output="${disk_output}  ${sata_model} *${sata_count}"
    else
        disk_output="${sata_model} *${sata_count}"
    fi
fi

echo "$disk_output"
# 网卡信息
echo -n "网卡: "
if command -v lshw >/dev/null 2>&1; then
    # 获取物理网卡信息，排除虚拟接口
    net_info=$(lshw -class network -short 2>/dev/null | grep -v "H/W path" | grep -v "^$" | grep -v "Wireless" | grep -E "eth|ens|enp" | head -1)
    if [ ! -z "$net_info" ]; then
        # 提取网卡型号，从第4列开始
        net_model=$(echo "$net_info" | awk '{for(i=4;i<=NF;i++) printf "%s ", $i}' | sed 's/[ \t]*$//')
        # 统计相同型号网卡数量
        net_count=$(lshw -class network -short 2>/dev/null | grep -v "H/W path" | grep -v "^$" | grep -v "Wireless" | grep -E "eth|ens|enp" | wc -l)
        echo "${net_model} *${net_count}"
    else
    echo "无法获取网卡详细信息"
fi
else
    echo "无法获取网卡详细信息"
fi

# 电源信息
echo -n "电源: "
if command -v dmidecode >/dev/null 2>&1; then
    power_model=$(dmidecode -t 39 | grep "Model Part Number" | head -1 | awk -F: '{print $2}' | sed 's/^[ \t]*//' | sed 's/[ \t]*$//')
    power_count=$(dmidecode -t 39 | grep "Model Part Number" | grep -v "Not Specified" | wc -l)
    if [ ! -z "$power_model" ] && [ "$power_count" -gt 0 ]; then
        echo "${power_model} *${power_count}"
else
        echo "无法获取电源信息"
    fi
else
    echo "无法获取电源信息"
fi

echo "=========================================="
EOF

# 使用sshpass连接并执行脚本
if command -v sshpass >/dev/null 2>&1; then
    sshpass -p "$PASSWORD" ssh -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null -o LogLevel=ERROR "$USERNAME@$IP" 'bash -s' < $TEMP_SCRIPT
else
    echo "警告: sshpass 未安装，将尝试使用expect..."
    if command -v expect >/dev/null 2>&1; then
        expect << EOF
spawn ssh -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null "$USERNAME@$IP"
expect "password:"
send "$PASSWORD\r"
expect "$ "
send "$(cat $TEMP_SCRIPT)\r"
expect "$ "
send "exit\r"
expect eof
EOF
    else
        echo "错误: 需要安装 sshpass 或 expect 来自动登录"
        echo "手动登录命令: ssh $USERNAME@$IP"
        echo "密码: $PASSWORD"
    fi
fi

# 清理临时文件
rm -f $TEMP_SCRIPT
