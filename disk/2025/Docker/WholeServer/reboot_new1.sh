#!/bin/bash
#============================================
#Author：胡泽志(<EMAIL>)
#0.1 release 板，实现基本功能，对整机进行reboot测试，每次boot起来之后检查一遍NVMe SSD是否正常识别
#参数1：reboot次数
#默认要执行reboot的次数为100次
#用法：./reboot.sh 100
#V1.1 release 板，实现基本功能，对整机进行reboot测试，每次boot起来之后检查一遍NVMe SSD是否正常识别,PCIe速率和Lan数是否发生变化
#V1.2 release 板，实现基本功能，对整机进行reboot测试，每次boot起来之sata HDD SSD 网卡等,检查rc.local文件是否包含reboot.sh的引用
#============================================
#平台对接参数入口
#domain="https://saturn.sankuai.com"
para=$1  #不为空的时候是第一次执行，为空的时候是后面执行的
# 统一定义日志和状态文件目录及变量
STATE_DIR="/home/<USER>/reboot"
LOG_DIR="$STATE_DIR"
cntfile="$STATE_DIR/cntfile"
totalcnt="$STATE_DIR/totalcnt"
reboottestID="$STATE_DIR/reboottestID"
initial_hw_info="$STATE_DIR/initial_hw_info"
initial_pcie_state="$STATE_DIR/initial_pcie_state"
initial_hdd_info="$STATE_DIR/initial_hdd_info"
initial_os_drive_info="$STATE_DIR/initial_os_drive_info"
baseline_hw_info="$STATE_DIR/baseline_hw_info"
current_hw_info="$STATE_DIR/current_hw_info"
previous_hw_info="$STATE_DIR/previous_hw_info"
prev_pcie_info="$STATE_DIR/prev_pcie_info"
curr_pcie_info="$STATE_DIR/curr_pcie_info"
hardware_change_report="$STATE_DIR/hardware_change_report.txt"
hardware_change_evidence="$STATE_DIR/hardware_change_evidence.zip"
# 其它日志文件
logfile="$LOG_DIR/reboot.log"
dmesg_hw_log="$LOG_DIR/dmesg_hw_change.log"
dmesg_pcie_log="$LOG_DIR/dmesg_pcie_change.log"
dmesg_nvme_log="$LOG_DIR/dmesg_nvme_rw_error.log"
dmesg_before="$LOG_DIR/dmesg_before.log"
dmesg_after="$LOG_DIR/dmesg_after.log"
dmesg_diff="$LOG_DIR/dmesg_diff.log"
mdfile="$LOG_DIR/reboot_report.md"
mkdir -p "$STATE_DIR"

# 如果传入了参数，强制清理所有历史数据，确保是全新测试
if [ -n "$para" ]; then
    rm -f "$cntfile" "$totalcnt" "$reboottestID" "$initial_hw_info" "$initial_pcie_state"
fi

# 新增：检查参数是否为正整数的函数
check_parameter() {
    # 如果已经存在计数文件，说明不是首次执行，跳过参数检查
    if [ -f "$cntfile" ] && [ -f "$totalcnt" ]; then
        echo "重启后继续执行，跳过参数检查"
        return 0
    fi

    # 检查参数是否存在
    if [ -z "$para" ]; then
        echo "错误：未提供参数"
        return 1
    fi

    # 检查参数是否为数字
    if ! [[ "$para" =~ ^[0-9]+$ ]]; then
        echo "错误：参数必须是正整数"
        return 1
    fi

    # 检查参数是否大于0
    if [ "$para" -le 0 ]; then
        echo "错误：参数必须大于0"
        return 1
    fi

    return 0
}

# 在处理参数之前先进行检查
if ! check_parameter "$para"; then
    echo "参数检查失败，退出测试"
    mkdir -p "$LOG_DIR"
    echo "参数检查失败，退出测试" >> "$logfile"
    exit 1
fi

# 在脚本前部定义脚本名变量
script_basename=$(basename "$0")

# 新增：精简网卡日志输出
get_nic_log_info() {
    lspci | grep -i ethernet | while read -r line; do
        bdf=$(echo "$line" | awk '{print $1}')
        name=$(echo "$line" | cut -d' ' -f2- | awk -F '[' '{print $2}' | awk -F ']' '{print $1}')
        netdir="/sys/bus/pci/devices/0000:$bdf/net/"
        netname=$(ls $netdir 2>/dev/null | head -1)
        if [ -n "$netname" ]; then
            speed=$(ethtool -i "$netname" 2>/dev/null | grep speed | awk '{print $2}')
        else
            speed="Unknown"
        fi
        lnksta=$(lspci -vvv -s $bdf 2>/dev/null | grep 'LnkSta:' | head -1 | awk -F 'LnkSta:' '{print $2}' | awk '{$1=$1;print}')
        echo "$bdf  $name  Speed: $speed  LnkSta: $lnksta"
    done
}

# 新增：精简NVMe日志输出
get_nvme_log_info() {
    for nvme in $(nvme list | awk 'NR>2 {print $1}'); do
        nvme_name=$(basename $nvme)
        bdf=$(ls -l /sys/block/$nvme_name/device | awk -F'../..' '{print $2}')
        lnksta=$(lspci -vvv -s $bdf 2>/dev/null | grep 'LnkSta:' | head -1 | awk -F 'LnkSta:' '{print $2}' | awk '{$1=$1;print}')
        printf "%s (%s):\t\tLnkSta: %s\n" "$nvme_name" "$bdf" "$lnksta"
    done
}

# 检查/etc/rc.d/rc.local文件中是否包含reboot.sh的引用
check_rc_local() {
    echo "Checking rc.local configuration at $(date)" >> $logfile
    # 删除所有 reboot*.sh 相关行
    sed -i "/\\/bin\\/bash .*reboot.*\\.sh/d" /etc/rc.local
    # 获取当前脚本绝对路径
    script_path=$(readlink -f "$0")
    # 写入 rc.local
    echo "/bin/bash $script_path &" >> /etc/rc.local
    chmod +x /etc/rc.local
    echo "Updated rc.local:" >> $logfile
}

get_pcie_info() {
    pcie_devices=$(lspci | wc -l)
    # shellcheck disable=SC2126
    lan_count=$(lspci | grep Ethernet | wc -l)

    # 获取PCIe速度信息
    pcie_speed_info=$(lspci -vvv | grep -E "LnkCap:|LnkSta:" | sort)

    # 保存到临时文件
    echo "$pcie_speed_info" > /tmp/pcie_speed_info_current.txt

    echo "$pcie_devices $lan_count"
}

# 新增：检查PCIe状态
compare_pcie_speed() {
    if [ -f "/tmp/pcie_speed_info_previous.txt" ]; then
        if ! diff -q "/tmp/pcie_speed_info_previous.txt" "/tmp/pcie_speed_info_current.txt" >/dev/null; then
            echo "PCIe speed changed. Stopping test." >> $logfile
            # 删除rc.local中的重启命令
            sed -i "/\/bin\/bash .*${script_basename}/d" /etc/rc.local
            exit 1
        fi
    fi
    # 更新前一次的PCIe速度信息
    mv /tmp/pcie_speed_info_current.txt /tmp/pcie_speed_info_previous.txt
}

# 修改check_pcie_state函数
check_pcie_state() {
    current_state=$(get_pcie_info)
    if [ ! -f "$initial_pcie_state" ]; then
        echo "$current_state" > "$initial_pcie_state"
        echo "Initial PCIe state saved: $current_state"
    else
        initial_state=$(cat "$initial_pcie_state")
        if [ "$current_state" != "$initial_state" ]; then
            echo "PCIe state changed. Initial: $initial_state, Current: $current_state"
            dmesg > "$dmesg_pcie_log"
            # 记录到日志
            echo "PCIe configuration changed during test!" >> "$logfile"
            # 删除rc.local中的重启命令
            sed -i "/\/bin\/bash .*${script_basename}/d" /etc/rc.local
            # 清理文件，防止重复执行
            rm -f "$cntfile" "$totalcnt"
            return 1
        fi
    fi
    # 比较PCIe速度信息
    compare_pcie_speed
    return 0
}

# 获取系统盘设备名
get_system_disk() {
    df / | awk 'NR==2 {print $1}' | sed 's/[0-9]*$//'
}

# 新增：检查并执行NVMe读写测试
check_nvme_rw() {
    echo "Starting NVMe read/write test at $(date)" >> "$logfile"
    for nvme in $(nvme list | awk 'NR>2 {print $1}'); do
        echo "Testing NVMe device: $nvme" >> "$logfile"
        fs_type=$(blkid -o value -s TYPE "$nvme")
        if [ -z "$fs_type" ]; then
            echo "No filesystem found on $nvme, skipping read/write test"
            continue
        fi
        mount_point=$(mktemp -d)
        if mount "$nvme" "$mount_point"; then
            if ! timeout 30s dd if=/dev/urandom of="$mount_point"/test_file bs=1M count=100 2>> "$logfile" || \
               ! timeout 30s dd if="$mount_point"/test_file of=/dev/null bs=1M 2>> "$logfile"; then
                echo "Read/write test failed for $nvme" >> "$logfile"
                umount "$mount_point"
                rm -rf "$mount_point"
                dmesg > "$dmesg_nvme_log"
                sed -i "/\/bin\/bash .*${script_basename}/d" /etc/rc.local
                return 1
            fi
            rm -f "$mount_point"/test_file
            umount "$mount_point"
        else
            echo "Failed to mount $nvme" >> "$logfile"
            rm -rf "$mount_point"
            return 1
        fi
        rm -rf "$mount_point"
    done
    return 0
}


# 修改：收集硬件详细信息函数，专注于关键硬件组件
collect_hardware_info() {
    local output_file=$1

    # 使用固定标题，不包含时间戳
    echo "=== Hardware Info Collection ===" > "$output_file"

    # 1. 存储设备信息 - HDD、SSD、NVMe SSD
    echo -e "\n=== Storage Devices ===" >> "$output_file"
    echo "--- SCSI/SATA Devices ---" >> "$output_file"
    lsscsi | sort >> "$output_file"
    echo "--- NVMe Devices ---" >> "$output_file"
    nvme list | grep -v "NSID" | sort >> "$output_file"

    # 2. 网卡和存储控制器信息
    echo -e "\n=== Network Controllers ===" >> "$output_file"
    lspci | grep -i "ethernet\|network" | sort >> "$output_file"
    echo -e "\n=== Storage Controllers ===" >> "$output_file"
    lspci | grep -i "raid\|sata\|scsi\|sas\|storage\|host bridge" | sort >> "$output_file"

    # 3. GPU信息
    echo -e "\n=== GPU Devices ===" >> "$output_file"
    lspci | grep -i "vga\|3d\|display\|nvidia\|amd" | sort >> "$output_file"

    # 4. PCIe速率和链路信息
    echo -e "\n=== PCIe Link Status ===" >> "$output_file"
    # 存储控制器PCIe信息
    echo "--- Storage Controller PCIe Info ---" >> "$output_file"
    lspci | grep -i "raid\|sata\|scsi\|sas\|storage\|nvme" | awk '{print $1}' | while read -r dev; do
        echo "Device: $dev" >> "$output_file"
        lspci -vvs "$dev" | grep -E "LnkCap:|LnkSta:" >> "$output_file"
    done

    # 网卡PCIe信息
    echo "--- Network Controller PCIe Info ---" >> "$output_file"
    lspci | grep -i "ethernet\|network" | awk '{print $1}' | while read -r dev; do
        echo "Device: $dev" >> "$output_file"
        lspci -vvs "$dev" | grep -E "LnkCap:|LnkSta:" >> "$output_file"
    done

    # GPU PCIe信息
    echo "--- GPU PCIe Info ---" >> "$output_file"
    lspci | grep -i "vga\|3d\|display\|nvidia\|amd" | awk '{print $1}' | while read -r dev; do
        echo "Device: $dev" >> "$output_file"
        lspci -vvs "$dev" | grep -E "LnkCap:|LnkSta:" >> "$output_file"
    done

    # 设备数量统计
    echo -e "\n=== Device Count Summary ===" >> "$output_file"
    echo "SATA/SCSI Drives: $(lsscsi | grep -c "disk")" >> "$output_file"
    echo "NVMe Drives: $(nvme list | grep -c "^/dev/nvme")" >> "$output_file"
    echo "Network Controllers: $(lspci | grep -c -i "ethernet\|network")" >> "$output_file"
    echo "Storage Controllers: $(lspci | grep -c -i "raid\|sata\|scsi\|sas\|storage\|host bridge")" >> "$output_file"
    echo "GPU Devices: $(lspci | grep -c -i "vga\|3d\|display\|nvidia\|amd")" >> "$output_file"
}

# 修改：比较硬件信息函数，专注于关键变化
compare_hardware_info() {
    local current_file="$current_hw_info"
    local previous_file="$previous_hw_info"
    local hw_change_detected=0
    local hw_change_details=""

    # 收集当前硬件信息
    collect_hardware_info "$current_file"

    # 如果是首次运行或基准文件丢失，尝试从持久化位置恢复
    if [ ! -f "$previous_file" ]; then
        if [ -f "$baseline_hw_info" ]; then
            echo "Restoring hardware baseline from persistent storage..." >> "$logfile"
            cp "$baseline_hw_info" "$previous_file"
        else
            echo "No baseline hardware information found, creating new baseline..." >> "$logfile"
            cp "$current_file" "$previous_file"
            cp "$current_file" "$baseline_hw_info"  # 同时保存到持久化位置
            cat "$current_file" >> "$logfile"
            return 0
        fi
    fi

    # 确保文件存在且不为空
    if [ ! -s "$previous_file" ]; then
        echo "Warning: Previous hardware info file is empty, creating new baseline..." >> "$logfile"
        cp "$current_file" "$previous_file"
        cp "$current_file" "$baseline_hw_info"
        cat "$current_file" >> "$logfile"
        return 0
    fi

    # 以下是原有的比较逻辑，保持不变
    # 1. 比较存储设备数量
    local prev_sata_count=$(grep "SATA/SCSI Drives:" "$previous_file" | awk '{print $3}')
    local curr_sata_count=$(grep "SATA/SCSI Drives:" "$current_file" | awk '{print $3}')
    local prev_nvme_count=$(grep "NVMe Drives:" "$previous_file" | awk '{print $3}')
    local curr_nvme_count=$(grep "NVMe Drives:" "$current_file" | awk '{print $3}')

    # 检查变量是否为空，如果为空则设置为0
    prev_sata_count=${prev_sata_count:-0}
    curr_sata_count=${curr_sata_count:-0}
    prev_nvme_count=${prev_nvme_count:-0}
    curr_nvme_count=${curr_nvme_count:-0}

    if [ "$prev_sata_count" != "$curr_sata_count" ]; then
        hw_change_detected=1
        hw_change_details+="SATA/SCSI drive count changed: $prev_sata_count -> $curr_sata_count\n"
    fi

    if [ "$prev_nvme_count" != "$curr_nvme_count" ]; then
        hw_change_detected=1
        hw_change_details+="NVMe drive count changed: $prev_nvme_count -> $curr_nvme_count\n"
    fi

    # 2. 比较网卡和存储控制器数量
    local prev_nic_count=$(grep "Network Controllers:" "$previous_file" | awk '{print $3}')
    local curr_nic_count=$(grep "Network Controllers:" "$current_file" | awk '{print $3}')
    local prev_storage_ctrl_count=$(grep "Storage Controllers:" "$previous_file" | awk '{print $3}')
    local curr_storage_ctrl_count=$(grep "Storage Controllers:" "$current_file" | awk '{print $3}')

    # 检查网卡和存储控制器数量变量
    prev_nic_count=${prev_nic_count:-0}
    curr_nic_count=${curr_nic_count:-0}
    prev_storage_ctrl_count=${prev_storage_ctrl_count:-0}
    curr_storage_ctrl_count=${curr_storage_ctrl_count:-0}

    if [ "$prev_nic_count" != "$curr_nic_count" ]; then
        hw_change_detected=1
        hw_change_details+="Network controller count changed: $prev_nic_count -> $curr_nic_count\n"
            fi

    if [ "$prev_storage_ctrl_count" != "$curr_storage_ctrl_count" ]; then
        hw_change_detected=1
        hw_change_details+="Storage controller count changed: $prev_storage_ctrl_count -> $curr_storage_ctrl_count\n"
        fi

    # 3. 比较GPU数量
    local prev_gpu_count=$(grep "GPU Devices:" "$previous_file" | awk '{print $3}')
    local curr_gpu_count=$(grep "GPU Devices:" "$current_file" | awk '{print $3}')

    # 检查网卡和存储控制器数量变量
    prev_gpu_count=${prev_gpu_count:-0}
    curr_gpu_count=${curr_gpu_count:-0}

    if [ "$prev_gpu_count" != "$curr_gpu_count" ]; then
        hw_change_detected=1
        hw_change_details+="GPU count changed: $prev_gpu_count -> $curr_gpu_count\n"
    fi

    # 4. 比较PCIe速率和链路状态
    # 提取PCIe链路信息部分
    grep -A 20 "=== PCIe Link Status ===" "$previous_file" > /tmp/prev_pcie_info
    grep -A 20 "=== PCIe Link Status ===" "$current_file" > /tmp/curr_pcie_info

    # 比较PCIe链路信息
    if [ -f "/tmp/prev_pcie_info" ] && [ -f "/tmp/curr_pcie_info" ] && ! diff -q /tmp/prev_pcie_info /tmp/curr_pcie_info >/dev/null; then
        # 检查是否有关键PCIe变化（速率或宽度）
        if diff /tmp/prev_pcie_info /tmp/curr_pcie_info | grep -q "LnkSta: Speed\|Width"; then
            hw_change_detected=1
            hw_change_details+="PCIe link status changed (speed or width)\n"
            hw_change_details+=$(diff /tmp/prev_pcie_info /tmp/curr_pcie_info | grep -A 1 -B 1 "LnkSta: Speed\|Width")
            hw_change_details+="\n"
        fi
    fi

    # 如果检测到硬件变化
    if [ $hw_change_detected -eq 1 ]; then
        # 创建硬件变化详细报告
        echo "=== Hardware Change Report $(date) ===" > "$hardware_change_report"
        echo -e "Hardware configuration changed during test!\n" >> "$hardware_change_report"
        echo -e "=== Change Details ===\n" >> "$hardware_change_report"
        echo -e "$hw_change_details" >> "$hardware_change_report"
        echo -e "\n=== Previous configuration ===\n" >> "$hardware_change_report"
        cat "$previous_file" >> "$hardware_change_report"
        echo -e "\n=== Current configuration ===\n" >> "$hardware_change_report"
        cat "$current_file" >> "$hardware_change_report"

        # 记录到日志
        echo "Hardware configuration changed!" >> "$logfile"
        echo -e "$hw_change_details" >> "$logfile"

        # 保存系统日志
        dmesg > "$dmesg_hw_log"

        # 将硬件变化报告和系统日志打包
        zip -r "$hardware_change_evidence" "$hardware_change_report" "$dmesg_hw_log" "$logfile"

        # 设置Result变量
        Result="Hardware configuration changed during test! See detailed report in hardware_change_evidence.zip"

        # 发送结果到Saturn系统
        curl -X POST \
            -F "resultFile=@$hardware_change_evidence" \
            -F "taskId=$task_id" \
            -F 'taskIndexListStr=[{"indexTag":"RebootTestResult","indexValue":"'"$Result"'"}]' \
            "$domain/saturn/script/callback" >> /tmp/curl_hw_change.log 2>&1

        # 清理临时文件
        rm -f /tmp/prev_pcie_info /tmp/curr_pcie_info
        return 1
    fi

    # 更新前一次的硬件信息
    cp "$current_file" "$previous_file"

    # 清理临时文件
    rm -f /tmp/prev_pcie_info /tmp/curr_pcie_info 2>/dev/null

    return 0
}

# 新增：获取系统硬件信息函数
get_hardware_info() {
    # 获取SATA设备信息
    sata_drives=$(lsblk -d -o NAME,TRAN | grep "sata" | wc -l)

    # 获取NVMe设备数量
    nvme_drives=$(nvme list | grep -c "^/dev/nvme")

    # 获取网卡信息
    nic_count=$(ip link show | grep -c "^[0-9]")
    active_nics=$(ip link show | grep "state UP" | wc -l)

    # 获取PCIe设备总数
    pcie_devices=$(lspci | wc -l)

    echo "SATA:$sata_drives NVMe:$nvme_drives NICs:$nic_count:$active_nics PCIe:$pcie_devices"
}

# 在脚本结束前添加
cleanup_temp_files() {
    rm -f /tmp/pcie_speed_info_previous.txt /tmp/pcie_speed_info_current.txt
    rm -f /tmp/prev_pcie_info /tmp/curr_pcie_info
    rm -f "$hardware_change_report" /tmp/pcie_change_report.txt
}

# 新增：dmesg error检查函数
check_dmesg_error() {
    dmesg > "$dmesg_after"
    diff "$dmesg_before" "$dmesg_after" | grep -iE 'error|fail|fatal|critical' > "$dmesg_diff"
    if [ -s "$dmesg_diff" ]; then
        echo "检测到新的dmesg error，停止测试" >> "$logfile"
        cat "$dmesg_diff" >> "$logfile"
        return 1
    fi
    return 0
}

# 新增：获取所有非OS NVMe盘
get_non_os_nvme_list() {
    os_disk=$(get_system_disk | xargs basename)
    nvme_list=()
    for nvme in $(nvme list | awk 'NR>2 {print $1}' | xargs -n1 basename); do
        if [ "$nvme" != "$os_disk" ]; then
            nvme_list+=("$nvme")
        fi
    done
    echo "${nvme_list[@]}"
}

# 新增：对所有非OS NVMe盘做30秒读写
check_nvme_rw_all() {
    local failed=0
    local nvme_list=( $(get_non_os_nvme_list) )
    for nvme in "${nvme_list[@]}"; do
        mount_point=$(mktemp -d)
        dev="/dev/$nvme"
        if mount $dev $mount_point 2>>"$logfile"; then
            if ! timeout 30s dd if=/dev/urandom of=$mount_point/test_file bs=1M count=30 oflag=direct 2>>"$logfile" || \
               ! timeout 30s dd if=$mount_point/test_file of=/dev/null bs=1M 2>>"$logfile"; then
                echo "NVMe $dev 30秒读写测试失败" >> "$logfile"
                failed=1
            fi
            rm -f $mount_point/test_file
            umount $mount_point
        else
            echo "NVMe $dev 挂载失败" >> "$logfile"
            failed=1
        fi
        rm -rf $mount_point
    done
    return $failed
}

# 修正：获取OS盘型号（完整，带model和serial）
get_os_drive_info() {
    os_disk=$(get_system_disk | xargs basename)
    lsblk -d -o NAME,MODEL,SERIAL | awk -v osd="$os_disk" 'NR>1 && $1==osd {if($3!="") print $1": "$2" "$3; else print $1": "$2}'
}

# 修正：统计所有HDD型号和数量（完整型号，排除MODEL字段）
get_hdd_info() {
    os_disk=$(get_system_disk | xargs basename)
    if command -v lsscsi &>/dev/null; then
        # 用lsscsi获取详细型号
        lsscsi | awk -v osd="$os_disk" '
            $0 ~ /disk/ && $NF!="/dev/"osd { 
                # 取品牌+型号+版本号
                for(i=3;i<=NF-1;++i) printf "%s ", $i; print ""
            }' | sed 's/ *$//' | sort | uniq -c | awk '{out=$2; for(i=3;i<=NF;i++) out=out" "$i; print out" * "$1}' | paste -sd ' ; ' -
    else
        # 退化到lsblk
        lsblk -d -o NAME,MODEL | awk -v osd="$os_disk" 'NR>1 && $1!=osd {print $2}' | grep -v "^MODEL$" | sort | uniq -c | awk '{print $2" * "$1}' | paste -sd ' ; ' -
    fi
}

# 新增：采集主机SN、型号、BMC、BIOS等
get_host_info() {
    local sn model bios bmc
    sn=$(dmidecode -s system-serial-number 2>/dev/null | head -1)
    model=$(dmidecode -s system-product-name 2>/dev/null | head -1)
    bios=$(dmidecode -s bios-version 2>/dev/null | head -1)
    bmc=$(ipmitool mc info 2>/dev/null | grep 'Firmware Revision' | awk -F: '{print $2}' | xargs)
    echo "- 主机SN: ${sn:-未知}"
    echo "- 主机型号: ${model:-未知}"
    echo "- BIOS版本: ${bios:-未知}"
    echo "- BMC版本: ${bmc:-未知}"
}

# 新增：保存/比对HDD信息
save_hdd_info() {
    get_hdd_info > "$initial_hdd_info"
    get_os_drive_info > "$initial_os_drive_info"
}
compare_hdd_info() {
    curr_hdd_info=$(get_hdd_info)
    curr_os_info=$(get_os_drive_info)
    prev_hdd_info=$(cat "$initial_hdd_info" 2>/dev/null)
    prev_os_info=$(cat "$initial_os_drive_info" 2>/dev/null)
    if [ "$curr_hdd_info" != "$prev_hdd_info" ]; then
        echo "HDD型号或数量发生变化: $prev_hdd_info -> $curr_hdd_info" >> "$logfile"
        return 1
    fi
    if [ "$curr_os_info" != "$prev_os_info" ]; then
        echo "OS盘型号发生变化: $prev_os_info -> $curr_os_info" >> "$logfile"
        return 1
    fi
    return 0
}

# 修改md报告生成，循环次数直接用totalcnt，OS盘和HDD型号完整
# generate_md_report(已完成次数，总次数)
generate_md_report() {
    local loops="$1"
    local totalcnt="$2"
    echo "# Reboot测试报告" > "$mdfile"
    get_host_info >> "$mdfile"
    echo "- 测试时间: $(date)" >> "$mdfile"
    echo "- 总循环次数: $totalcnt/$totalcnt" >> "$mdfile"
    echo "- OS盘型号: $(cat "$initial_os_drive_info" 2>/dev/null)" >> "$mdfile"
    echo "- HDD信息: $(cat "$initial_hdd_info" 2>/dev/null)" >> "$mdfile"
    echo "- 测试日志: [reboot.log](./reboot.log)" >> "$mdfile"
    echo "\n## 每次循环硬件快照（部分)" >> "$mdfile"
    grep '^========== Loop' "$logfile" | tail -20 >> "$mdfile"
    echo "\n## 终止原因/异常记录" >> "$mdfile"
    local errlog=$(grep -E '失败|变化|error|fail|fatal|critical|停止测试' "$logfile" | tail -20)
    if [ -z "$errlog" ]; then
        echo "无" >> "$mdfile"
    else
        echo "$errlog" >> "$mdfile"
    fi
    echo "\n## 最终硬件状态" >> "$mdfile"
    tail -40 "$logfile" >> "$mdfile"
}

# 修改check_hardware函数
check_hardware() {
    cnt=$(cat "$cntfile")
    totalcnt=$(cat "$totalcnt" | tr -d '[:space:]')
    log_file="$logfile"
    if [ ! -f "$log_file" ]; then
        touch "$log_file"
        echo "Created new log file: $log_file"
    fi
    latest_file="$log_file"
    echo "The latest file is: $latest_file"
    echo "============================="
    echo "cnt=$cnt  totalcnt=$totalcnt"
    echo "============================="
    Time=$(date +%D_%T)
    echo -e "\n========== Loop $cnt  $Time ==============" >> "$logfile"
    echo "[NIC Info]" >> "$logfile"
    get_nic_log_info >> "$logfile"
    echo "[NVMe Info]" >> "$logfile"
    get_nvme_log_info >> "$logfile"
    echo "[HDD Info]" >> "$logfile"
    echo "HDD: $(get_hdd_info)" >> "$logfile"
    echo "OS drive: $(get_os_drive_info)" >> "$logfile"
    # 新增：重启后优先检测nvme/hdd
    nvme_count=$(nvme list | awk 'NR>2 {print $1}' | wc -l)
    if [ "$nvme_count" -gt 0 ]; then
        echo "检测到NVMe SSD，开始30秒读写测试..." >> "$logfile"
        if ! check_nvme_rw_all; then
            echo "NVMe SSD 30秒读写测试失败，终止测试" >> "$logfile"
            sed -i "/\/bin\/bash .*${script_basename}/d" /etc/rc.local
            exit 1
        fi
    else
        echo "未检测到NVMe SSD" >> "$logfile"
        if ! compare_hdd_info; then
            echo "HDD型号或数量发生变化，终止测试" >> "$logfile"
            sed -i "/\/bin\/bash .*${script_basename}/d" /etc/rc.local
            exit 1
        fi
    fi
    # 检查硬件配置
    if ! compare_hardware_info; then
        echo "Hardware configuration changed, stopping test..." >> "$logfile"
        sed -i "/\/bin\/bash .*${script_basename}/d" /etc/rc.local
        rm -f "$reboottestID" "$cntfile" "$totalcnt"
        exit 1
    fi

    # 检查dmesg error
    if ! check_dmesg_error; then
        sed -i "/\/bin\/bash .*${script_basename}/d" /etc/rc.local
        rm -f "$reboottestID" "$cntfile" "$totalcnt"
        exit 1
    fi

    # 4. 判断是否到达测试次数
    if [ "$cnt" -ge "$totalcnt" ]; then
        echo "Reached target count: $cnt >= $totalcnt" >> "$logfile"
        echo "The reboot test finished successfully after $cnt loops" >> "$logfile"
        echo "Test completed at $(date)" >> "$logfile"
        sed -i "/\/bin\/bash .*${script_basename}/d" /etc/rc.local
        echo "Removed reboot command from rc.local" >> "$logfile"
        rm -f "$reboottestID" "$cntfile" "$totalcnt"
        generate_md_report $cnt $totalcnt
        exit 0
    else
        echo "Loop $cnt test finished.............." >> "$logfile"
        echo "Hardware check completed successfully" >> "$logfile"
        echo "Preparing to reboot system in 60 seconds..." >> "$logfile"
        echo "Current time: $(date)" >> "$logfile"
        sync
        sleep 60s
        echo "Executing reboot command now..." >> "$logfile"
        sync
        echo "即将重启系统..." >> "$logfile"
        shutdown -r now
    fi
}

# 新增：检查并安装必要工具
check_and_install_tools() {
    echo "Checking required tools..." >> "$logfile"
    local required_tools=("diff" "lsscsi" "nvme" "ethtool" "lspci" "curl" "wget" "dd")
    local missing_tools=()

    for tool in "${required_tools[@]}"; do
        if ! command -v "$tool" &> /dev/null; then
            missing_tools+=("$tool")
            echo "Missing tool: $tool" >> "$logfile"
        fi
    done

    if [ ${#missing_tools[@]} -gt 0 ]; then
        echo "Installing missing tools..." >> "$logfile"

        # 检测包管理器
        if command -v yum &> /dev/null; then
            # CentOS/RHEL
            for tool in "${missing_tools[@]}"; do
                case "$tool" in
                    "lsscsi")
                        yum install -y lsscsi >> "$logfile" 2>&1
                        ;;
                    "nvme")
                        yum install -y nvme-cli >> "$logfile" 2>&1
                        ;;
                    "ethtool")
                        yum install -y ethtool >> "$logfile" 2>&1
                        ;;
                    "lspci")
                        yum install -y pciutils >> "$logfile" 2>&1
                        ;;
                    "curl")
                        yum install -y curl >> "$logfile" 2>&1
                        ;;
                    "wget")
                        yum install -y wget >> "$logfile" 2>&1
                        ;;
                    "diff")
                        yum install -y diffutils >> "$logfile" 2>&1
                        ;;
                    *)
                        echo "Unknown tool: $tool, skipping installation" >> "$logfile"
                        ;;
                esac
            done
        elif command -v apt-get &> /dev/null; then
            # Debian/Ubuntu
            apt-get update >> "$logfile" 2>&1
            for tool in "${missing_tools[@]}"; do
                case "$tool" in
                    "lsscsi")
                        apt-get install -y lsscsi >> "$logfile" 2>&1
                        ;;
                    "nvme")
                        apt-get install -y nvme-cli >> "$logfile" 2>&1
                        ;;
                    "ethtool")
                        apt-get install -y ethtool >> "$logfile" 2>&1
                        ;;
                    "lspci")
                        apt-get install -y pciutils >> "$logfile" 2>&1
                        ;;
                    "curl")
                        apt-get install -y curl >> "$logfile" 2>&1
                        ;;
                    "wget")
                        apt-get install -y wget >> "$logfile" 2>&1
                        ;;
                    "diff")
                        apt-get install -y diffutils >> "$logfile" 2>&1
                        ;;
                    *)
                        echo "Unknown tool: $tool, skipping installation" >> "$logfile"
                        ;;
                esac
            done
        else
            echo "Unsupported package manager, please install missing tools manually: ${missing_tools[*]}" >> "$logfile"
            return 1
            fi

        # 验证安装
        local still_missing=()
        for tool in "${missing_tools[@]}"; do
            if ! command -v "$tool" &> /dev/null; then
                still_missing+=("$tool")
            fi
        done

        if [ ${#still_missing[@]} -gt 0 ]; then
            echo "Failed to install some tools: ${still_missing[*]}" >> "$logfile"
            return 1
    else
            echo "All required tools installed successfully" >> "$logfile"
            fi
    else
        echo "All required tools are already installed" >> "$logfile"
        fi

    return 0
}

# 修改init函数，首次执行时保存HDD/OS盘信息
init() {
    echo "============================================" >> "$logfile"
    echo "Starting init function at $(date)" >> "$logfile"
    check_and_install_tools
    if [ -n "$para" ]; then # 首次执行
        echo "The first time for test,remove files first............"
        rm -f "$cntfile" "$reboottestID" "$totalcnt" "$current_hw_info" "$previous_hw_info"
        rm -f "$logfile" "$mdfile" "$dmesg_hw_log" "$dmesg_pcie_log" "$dmesg_nvme_log" "$dmesg_before" "$dmesg_after" "$dmesg_diff"
        echo $task_id > "$reboottestID"
        echo ${para} > "$totalcnt"
        echo "1" > "$cntfile"
        echo "Collecting initial hardware information as baseline..." >> "$logfile"
        collect_hardware_info "$previous_hw_info"
        cp "$previous_hw_info" "$baseline_hw_info"
        cat "$previous_hw_info" >> "$logfile"
        get_pcie_info > "$initial_pcie_state"
        save_hdd_info
        check_rc_local
        echo "首次执行后即将重启系统..." >> "$logfile"
        shutdown -r now
    else
        # 断点续跑，cntfile 必须存在
        if [ ! -f "$previous_hw_info" ] && [ -f "$baseline_hw_info" ]; then
            echo "Restoring hardware baseline from persistent storage..." >> "$logfile"
            cp "$baseline_hw_info" "$previous_hw_info"
        fi
        if [ ! -f "$cntfile" ] || [ ! -f "$totalcnt" ]; then
            echo "计数文件丢失，无法断点续跑，退出" >> "$logfile"
            exit 1
        fi
        cnt=$(cat "$cntfile")
        let cnt=cnt+1
        echo $cnt > "$cntfile"
        echo "loop: $cnt" >> "$logfile"
    fi
    rm -f /tmp/pcie_speed_info_previous.txt /tmp/pcie_speed_info_current.txt
    dmesg > "$dmesg_before"
}


init
check_hardware
