#!/bin/bash

while getopts i:d:a: opt;
do
  case $opt in
  i)
    task_id=$OPTARG
    echo "task_id : $task_id"
   ;;
  d)
    domain=$OPTARG
    echo "domain : $domain"
   ;;
  a)
    para=$OPTARG
    echo "para : $para"
   ;;
  esac
done

mlc_install()
{
mkdir -p /home/<USER>/MLC /home/<USER>
rm -rf /home/<USER>/MLC/*  /home/<USER>/mlc.log
echo 4000 > /proc/sys/vm/nr_hugepages
echo always > /sys/kernel/mm/transparent_hugepage/enabled
echo always > /sys/kernel/mm/transparent_hugepage/defrag

cd /home/<USER>/MLC
wget https://s3plus.sankuai.com/cjhtest/mlc
}

mlc_test()
{
echo "MLC test starting......" >> ./mlc.log 2>&1
echo "Running command: ./mlc -e -r ---------------------------" >> ./mlc.log 2>&1
./mlc -e -r >> ./mlc.log 2>&1
echo "Running command: ./mlc ---------------------------------" >> ./mlc.log 2>&1
./mlc >> ./mlc.log 2>&1
echo "MLC test finished!" >> ./mlc.log 2>&1
}

callback()
{
# cd /home/<USER>
# mlc=`cat mlc.log`
Execution_time=$((end_time - start_time))" seconds"
curl -X POST -F "resultFile=@/home/<USER>/mlc.log" -F "taskId=$task_id" -F  'taskIndexListStr=[{"indexTag":"Execution_time","indexValue":"'"$Execution_time"'"}]' "$domain/saturn/script/callback"
}

mlc_install

cd /home/<USER>/MLC/
chmod +x mlc

start_time=$(date +%s)
for i in $(seq 1 $para)
do
    echo "-------------------------------------" >> ./mlc.log 2>&1
    echo "Running test $i of $para" >> ./mlc.log 2>&1
    mlc_test
done
end_time=$(date +%s)
mv mlc.log /home/<USER>/

callback

##下面代码的功能是反馈脚本执行异常
if [ $? -eq 0 ];
then
echo "脚本执行成功！"
else
echo "******给saturn系统反馈脚本执行失败******"
curl "$domain/saturn/script/failure?taskId=$task_id"
fi
