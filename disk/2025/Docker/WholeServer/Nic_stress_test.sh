#!/bin/bash

set -euo pipefail

echo "警告：请确保已经设置好 SSH 密钥认证。"
convert_password_code() {
    local code=$1
    local lower_code=$(echo "$code" | tr '[:upper:]' '[:lower:]')
    case "$lower_code" in
        keym) echo "KeyM" ;;
        keyo) echo "KeyO" ;;
        *) echo "$code" ;;  # 如果没有匹配的转换，返回原始输入
    esac
}

while getopts i:d:a: opt; do
    case $opt in
    i)
        task_id=$OPTARG
        echo "task_id : $task_id"
        ;;
    d)
        domain=$OPTARG
        echo "domain : $domain"
        ;;
    a)
        IFS=',' read -r hostname password_code_input <<< "$OPTARG"
        target_host=$hostname
        password_code=$(convert_password_code "$password_code_input")
        echo "target_host : $target_host"
        echo "password_code : $password_code"
        ;;
    esac
done

# 移除之前在 getopts 循环之后的分割代码

if [ -z "${target_host:-}" ] || [ -z "${password_code:-}" ]; then
    echo "错误：未指定目标主机或密码代号。请使用格式 '主机名,密码代号' 作为参数。" >&2
    exit 1
fi


LOG_DIR="/home/<USER>/$(date +%Y%m%d_%H%M%S)"
mkdir -p "${LOG_DIR}"
SSH_KEY_PATH="$HOME/.ssh/id_rsa"
TEST_DURATION=60
PARALLEL_STREAMS=16
PING_COUNT=100

PASSWORD_SERVER="************"
PASSWORD_PATH="/huzz"

get_password() {
    local temp_file=$(mktemp)
    local url="http://${PASSWORD_SERVER}${PASSWORD_PATH}/${password_code}"
    echo "正在从 $url 获取密码..." >&2
    if ! wget -q -O "$temp_file" "$url"; then
    	echo "无法从服务器获取密码文件: $url" >&2
        echo "wget 退出状态: $?" >&2
        rm -f "$temp_file"
        return 1
    fi
    if [ ! -s "$temp_file" ]; then
        echo "获取的密码文件为空" >&2
        rm -f "$temp_file"
        return 1
    fi
    cat "$temp_file"
    rm -f "$temp_file"
}


SSH_PASSWORD=$(get_password)
if [ $? -ne 0 ] || [ -z "$SSH_PASSWORD" ]; then
    echo "无法获取有效的密码，脚本退出" >&2
    exit 1
fi


check_ssh_key() {
    if [ ! -f "$SSH_KEY_PATH" ]; then
        echo "SSH密钥不存在，正在生成..."
        ssh-keygen -t rsa -N "" -f "$SSH_KEY_PATH"
    fi

    echo "正在复制SSH公钥到目标主机..."
    sshpass -p "$SSH_PASSWORD" ssh-copy-id -o StrictHostKeyChecking=no -i "${SSH_KEY_PATH}.pub" "root@$target_host"

    if [ $? -ne 0 ]; then
        echo "无法复制SSH公钥到目标主机。请检查连接和密码。" >&2
        exit 1
    fi

    echo "SSH密钥设置成功"
}

install_tools() {
    for tool in iperf3 wget sshpass; do
        if ! command -v $tool &> /dev/null; then
            echo "正在安装 $tool..."
            if command -v apt-get &> /dev/null; then
                sudo apt-get update && sudo apt-get install -y $tool
            elif command -v yum &> /dev/null; then
                sudo yum install -y $tool
            else
                echo "无法安装 $tool，请手动安装后重试。" >&2
                exit 1
            fi
        fi
    done
}


remote_exec() {
    if ! ssh -i "$SSH_KEY_PATH" -o StrictHostKeyChecking=no "root@$target_host" "$1"; then
        echo "远程执行命令失败: $1" >&2
        echo "请确保SSH密钥已正确配置到目标主机。" >&2
        exit 1
    fi
}

install_tools_remote() {
    remote_exec "$(declare -f install_tools); install_tools"
}

run_iperf_server() {
    local host=$1
    if [ "$host" = "local" ]; then
        iperf3 -s > "${LOG_DIR}/iperf_server_local.log" 2>&1 &
        echo $! > "${LOG_DIR}/iperf_server_local.pid"
    else
        remote_exec "iperf3 -s > /tmp/iperf_server_remote.log 2>&1 & echo \$! > /tmp/iperf_server_remote.pid"
    fi
    sleep 2
}

stop_iperf_server() {
    local host=$1
    if [ "$host" = "local" ]; then
        if [ -f "${LOG_DIR}/iperf_server_local.pid" ]; then
            kill "$(cat "${LOG_DIR}/iperf_server_local.pid")"
            rm "${LOG_DIR}/iperf_server_local.pid"
        fi
    else
        remote_exec "if [ -f /tmp/iperf_server_remote.pid ]; then kill \$(cat /tmp/iperf_server_remote.pid); rm /tmp/iperf_server_remote.pid; fi"
    fi
}

run_iperf_client() {
    local server_ip=$1
    local output_file=$2
    iperf3 -c "$server_ip" -P "$PARALLEL_STREAMS" -t "$TEST_DURATION" -i 1 -J > "$output_file"
}

parse_iperf_result() {
    local file=$1
    local bandwidth=$(jq -r '.end.sum_received.bits_per_second // empty' "$file" 2>/dev/null | awk '{printf "%.2f", $1/1000000000}')
    if [ -z "$bandwidth" ]; then
        echo "N/A Gbits/sec"
    else
        echo "$bandwidth Gbits/sec"
    fi
}

run_ping_test() {
    local target=$1
    local output_file=$2
    ping -c $PING_COUNT $target | tee "$output_file"
}

parse_ping_result() {
    local file=$1
    local avg_latency=$(awk -F '/' '/rtt min\/avg\/max\/mdev/ {print $5}' "$file")
    local packet_loss=$(awk '/packet loss/ {match($0, /([0-9.]+)%/); print substr($0, RSTART, RLENGTH-1)}' "$file")

    avg_latency=${avg_latency:-"N/A"}
    packet_loss=${packet_loss:-"N/A"}

    echo "avg_latency=${avg_latency}ms packet_loss=${packet_loss}%"
}

scp_with_key() {
    if ! scp -i "$SSH_KEY_PATH" -o StrictHostKeyChecking=no "$1" "$2"; then
        echo "无法传输文件: $1 -> $2" >&2
        echo "请确保SSH密钥已正确配置到目标主机。" >&2
        exit 1
    fi
}
check_ssh_connection() {
    if ! ssh -i "$SSH_KEY_PATH" -o StrictHostKeyChecking=no -o BatchMode=yes "root@$target_host" exit; then
        echo "无法建立SSH连接到目标主机。请确保SSH密钥已正确配置。" >&2
        exit 1
    fi
}

run_test() {
    local local_ip
    local_ip=$(hostname -I | awk '{print $1}')
    local remote_ip
    remote_ip=$(remote_exec "hostname -I | awk '{print \$1}'")

    echo "本地 IP: $local_ip"
    echo "远程 IP: $remote_ip"

    echo "开始第一轮测试：本地作为服务器，远程作为客户端"
    run_iperf_server "local"
    remote_exec "iperf3 -c $local_ip -P $PARALLEL_STREAMS -t $TEST_DURATION -i 1 -J > /tmp/iperf_client_remote.log"
    stop_iperf_server "local"

    echo "开始第二轮测试：远程作为服务器，本地作为客户端"
    run_iperf_server "remote"
    run_iperf_client "$remote_ip" "${LOG_DIR}/iperf_client_local.log"
    stop_iperf_server "remote"

    echo "开始 ping 测试"
    run_ping_test "$remote_ip" "${LOG_DIR}/ping_local_to_remote.log"
    remote_exec "ping -c $PING_COUNT $local_ip | tee /tmp/ping_remote_to_local.log"

    # 修改scp命令
    scp_with_key "root@$target_host:/tmp/iperf_client_remote.log" "${LOG_DIR}/iperf_client_remote.log"
    scp_with_key "root@$target_host:/tmp/iperf_server_remote.log" "${LOG_DIR}/iperf_server_remote.log"
    scp_with_key "root@$target_host:/tmp/ping_remote_to_local.log" "${LOG_DIR}/ping_remote_to_local.log"

    local_server_result=$(parse_iperf_result "${LOG_DIR}/iperf_server_local.log")
    remote_client_result=$(parse_iperf_result "${LOG_DIR}/iperf_client_remote.log")
    remote_server_result=$(parse_iperf_result "${LOG_DIR}/iperf_server_remote.log")
    local_client_result=$(parse_iperf_result "${LOG_DIR}/iperf_client_local.log")
    local_to_remote_ping=$(parse_ping_result "${LOG_DIR}/ping_local_to_remote.log")
    remote_to_local_ping=$(parse_ping_result "${LOG_DIR}/ping_remote_to_local.log")

    echo "测试结果："
    echo "A->B 带宽: $local_client_result"
    echo "B->A 带宽: $remote_client_result"
    echo "A->B $local_to_remote_ping"
    echo "B->A $remote_to_local_ping"

    {
        echo "A->B 带宽: $local_client_result"
        echo "B->A 带宽: $remote_client_result"
        echo "A->B $local_to_remote_ping"
        echo "B->A $remote_to_local_ping"
    } > "${LOG_DIR}/test_results.txt"
}

main() {
    check_ssh_key
    check_ssh_connection
    install_tools
    install_tools_remote
    run_test

    zip -r "$LOG_DIR/result.zip" "$LOG_DIR"/*

    rm -f "$LOG_DIR"/*.log
    remote_exec "rm -f /tmp/iperf_*.log /tmp/iperf_server_remote.pid /tmp/ping_*.log"

    echo "测试完成，报告保存在 ${LOG_DIR}/test_results.txt"
}

main

result_all=$(tr "\n" "#" < "${LOG_DIR}/test_results.txt")
echo "result_all:$result_all"
curl -X POST -F "resultFile=@$LOG_DIR/result.zip" -F "taskId=$task_id" -F 'taskIndexListStr=[{"indexTag":"test_result","indexValue":"'"$result_all"'"}]' "$domain/saturn/script/callback"

tag=$?
echo "curl tag: $tag"
if [ $tag -eq 0 ]; then
    echo "脚本执行成功！"
else
    echo "******给saturn系统反馈脚本执行失败******"
    curl "$domain/saturn/script/failure?taskId=$task_id"
fi


